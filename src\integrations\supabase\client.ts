// Supabase client configuration for Law App Morocco
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://stvxoaydqjutgqynewva.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN0dnhvYXlkcWp1dGdxeW5ld3ZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjYwMDksImV4cCI6MjA2NTE0MjAwOX0.HteYOHfynMrlueDm04LN0WgyWzD7tT6ZVltaEx_zdus";

// Vérification des variables d'environnement
if (!SUPABASE_URL || !SUPABASE_PUBLISHABLE_KEY) {
  throw new Error('Missing Supabase environment variables');
}

// Configuration du client Supabase avec options avancées
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Helper pour vérifier la connexion
export const testConnection = async () => {
  try {
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    if (error) throw error;
    return { success: true, message: 'Connexion Supabase réussie' };
  } catch (error: any) {
    return { success: false, message: error.message };
  }
};

// Export des constantes pour usage externe
export const SUPABASE_CONFIG = {
  url: SUPABASE_URL,
  anonKey: SUPABASE_PUBLISHABLE_KEY,
  projectId: 'stvxoaydqjutgqynewva'
};