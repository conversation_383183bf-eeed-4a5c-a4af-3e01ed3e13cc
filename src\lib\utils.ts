import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// API Integration utilities
export const API_ENDPOINTS = {
  DOCUMENT_GENERATION: 'https://benzaid.app.n8n.cloud/webhook/2e1dd36d-1458-4242-9462-0adfe5b12b1f',
  LEGAL_CHATBOT: 'https://benzaid.app.n8n.cloud/webhook/232f677d-6df9-403c-b53b-57ca7542d831/chat'
} as const;

export async function makeApiRequest(url: string, options: RequestInit = {}) {
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}
