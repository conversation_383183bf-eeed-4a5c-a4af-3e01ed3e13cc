import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  DocumentTemplate, 
  DocumentField,
  DocumentService,
  DocumentGenerationRequest 
} from '@/services/documentService';
import { 
  FileText, 
  Download, 
  Loader2,
  ArrowLeft,
  Save
} from 'lucide-react';

interface DocumentFormProps {
  template: DocumentTemplate;
  onBack: () => void;
  onDocumentGenerated: (documentId: string, downloadUrl: string) => void;
  userId: string;
}

export const DocumentForm: React.FC<DocumentFormProps> = ({
  template,
  onBack,
  onDocumentGenerated,
  userId
}) => {
  const { i18n } = useTranslation();
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const [documentTitle, setDocumentTitle] = useState('');
  const currentLanguage = i18n.language as 'ar' | 'fr';

  // Create dynamic schema based on template fields
  const createValidationSchema = () => {
    const schemaFields: Record<string, any> = {};
    
    template.fields.forEach(field => {
      let fieldSchema: any;
      
      switch (field.type) {
        case 'text':
          fieldSchema = z.string();
          if (field.validation?.min) {
            fieldSchema = fieldSchema.min(field.validation.min);
          }
          if (field.validation?.max) {
            fieldSchema = fieldSchema.max(field.validation.max);
          }
          if (field.validation?.pattern) {
            fieldSchema = fieldSchema.regex(new RegExp(field.validation.pattern));
          }
          break;
        case 'number':
          fieldSchema = z.coerce.number();
          if (field.validation?.min) {
            fieldSchema = fieldSchema.min(field.validation.min);
          }
          if (field.validation?.max) {
            fieldSchema = fieldSchema.max(field.validation.max);
          }
          break;
        case 'date':
          fieldSchema = z.string().min(1);
          break;
        case 'select':
          fieldSchema = z.string().min(1);
          break;
        case 'textarea':
          fieldSchema = z.string();
          if (field.validation?.min) {
            fieldSchema = fieldSchema.min(field.validation.min);
          }
          if (field.validation?.max) {
            fieldSchema = fieldSchema.max(field.validation.max);
          }
          break;
        case 'checkbox':
          fieldSchema = z.boolean();
          break;
        default:
          fieldSchema = z.string();
      }
      
      if (!field.required) {
        fieldSchema = fieldSchema.optional();
      }
      
      schemaFields[field.id] = fieldSchema;
    });
    
    return z.object(schemaFields);
  };

  const form = useForm({
    resolver: zodResolver(createValidationSchema()),
    defaultValues: template.fields.reduce((acc, field) => {
      acc[field.id] = field.type === 'checkbox' ? false : '';
      return acc;
    }, {} as Record<string, any>)
  });

  const getFieldLabel = (field: DocumentField) => {
    return currentLanguage === 'ar' ? field.nameAr : field.nameFr;
  };

  const getFieldPlaceholder = (field: DocumentField) => {
    return currentLanguage === 'ar' ? field.placeholderAr : field.placeholderFr;
  };

  const renderField = (field: DocumentField) => {
    const label = getFieldLabel(field);
    const placeholder = getFieldPlaceholder(field);
    const error = form.formState.errors[field.id];

    switch (field.type) {
      case 'text':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              placeholder={placeholder}
              {...form.register(field.id)}
              className={error ? 'border-red-500' : ''}
            />
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </div>
        );

      case 'number':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="number"
              placeholder={placeholder}
              {...form.register(field.id)}
              className={error ? 'border-red-500' : ''}
            />
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </div>
        );

      case 'date':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="date"
              {...form.register(field.id)}
              className={error ? 'border-red-500' : ''}
            />
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </div>
        );

      case 'select':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Select onValueChange={(value) => form.setValue(field.id, value)}>
              <SelectTrigger className={error ? 'border-red-500' : ''}>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option, index) => (
                  <SelectItem key={index} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </div>
        );

      case 'textarea':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Textarea
              id={field.id}
              placeholder={placeholder}
              rows={4}
              {...form.register(field.id)}
              className={error ? 'border-red-500' : ''}
            />
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </div>
        );

      case 'checkbox':
        return (
          <div key={field.id} className="flex items-center space-x-2 space-x-reverse">
            <Checkbox
              id={field.id}
              checked={form.watch(field.id)}
              onCheckedChange={(checked) => form.setValue(field.id, checked)}
            />
            <Label htmlFor={field.id} className="text-sm font-normal">
              {label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {error && (
              <p className="text-sm text-red-500">{error.message}</p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const onSubmit = async (data: Record<string, any>) => {
    if (!documentTitle.trim()) {
      toast({
        title: currentLanguage === 'ar' ? 'خطأ' : 'Erreur',
        description: currentLanguage === 'ar' 
          ? 'يرجى إدخال عنوان للوثيقة' 
          : 'Veuillez entrer un titre pour le document',
        variant: 'destructive'
      });
      return;
    }

    setIsGenerating(true);

    try {
      const request: DocumentGenerationRequest = {
        templateId: template.id,
        language: currentLanguage,
        data,
        userId,
        title: documentTitle
      };

      const response = await DocumentService.generateDocument(request);

      if (response.success && response.documentId && response.downloadUrl) {
        toast({
          title: currentLanguage === 'ar' ? 'تم بنجاح' : 'Succès',
          description: currentLanguage === 'ar' 
            ? 'تم إنشاء الوثيقة بنجاح' 
            : 'Document généré avec succès'
        });
        
        onDocumentGenerated(response.documentId, response.downloadUrl);
      } else {
        throw new Error(response.error || 'Document generation failed');
      }
    } catch (error) {
      console.error('Document generation error:', error);
      toast({
        title: currentLanguage === 'ar' ? 'خطأ' : 'Erreur',
        description: currentLanguage === 'ar' 
          ? 'فشل في إنشاء الوثيقة' 
          : 'Échec de la génération du document',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const templateDisplayName = currentLanguage === 'ar' ? template.nameAr : template.nameFr;

  return (
    <div className="w-full max-w-6xl mx-auto px-4 space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          {currentLanguage === 'ar' ? 'العودة' : 'Retour'}
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{templateDisplayName}</h1>
          <p className="text-gray-600">
            {currentLanguage === 'ar' ? 'املأ البيانات المطلوبة' : 'Remplissez les données requises'}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {currentLanguage === 'ar' ? 'معلومات الوثيقة' : 'Informations du document'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="document-title">
                {currentLanguage === 'ar' ? 'عنوان الوثيقة' : 'Titre du document'}
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="document-title"
                value={documentTitle}
                onChange={(e) => setDocumentTitle(e.target.value)}
                placeholder={
                  currentLanguage === 'ar' 
                    ? 'أدخل عنوان الوثيقة' 
                    : 'Entrez le titre du document'
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card>
          <CardHeader>
            <CardTitle>
              {currentLanguage === 'ar' ? 'بيانات النموذج' : 'Données du modèle'}
            </CardTitle>
            <CardDescription>
              {currentLanguage === 'ar' 
                ? 'املأ جميع الحقول المطلوبة لإنشاء الوثيقة' 
                : 'Remplissez tous les champs requis pour générer le document'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6">
              {template.fields.map(renderField)}
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onBack}>
            {currentLanguage === 'ar' ? 'إلغاء' : 'Annuler'}
          </Button>
          <Button type="submit" disabled={isGenerating}>
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {currentLanguage === 'ar' ? 'جاري الإنشاء...' : 'Génération...'}
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                {currentLanguage === 'ar' ? 'إنشاء الوثيقة' : 'Générer le document'}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
