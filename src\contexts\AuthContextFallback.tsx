import React, { createContext, useContext, useEffect, useState } from 'react';

interface Profile {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: 'admin' | 'user' | 'advocate';
  subscription_tier: 'free' | 'pro_user' | 'pro_advocate';
  subscription_end?: string;
  trial_end?: string;
  stripe_customer_id?: string;
  is_verified?: boolean;
}

interface User {
  id: string;
  email: string;
}

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName: string, role?: 'user' | 'advocate') => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<any>;
  checkSubscription: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProviderFallback: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(false); // Start with false for demo

  useEffect(() => {
    // Simulate loading for demo
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const signUp = async (email: string, password: string, fullName: string, role: 'user' | 'advocate' = 'user') => {
    console.log('Demo signup:', { email, fullName, role });
    
    // Simulate successful signup
    const mockUser = {
      id: `user_${Date.now()}`,
      email
    };
    
    const mockProfile = {
      id: mockUser.id,
      email,
      full_name: fullName,
      role,
      subscription_tier: 'free' as const,
      is_verified: role === 'user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    setUser(mockUser);
    setProfile(mockProfile);
    
    return { data: { user: mockUser }, error: null };
  };

  const signIn = async (email: string, password: string) => {
    console.log('Demo signin:', { email });
    
    // Simulate successful signin
    const mockUser = {
      id: `user_${Date.now()}`,
      email
    };
    
    const mockProfile = {
      id: mockUser.id,
      email,
      full_name: email.split('@')[0],
      role: 'user' as const,
      subscription_tier: 'free' as const,
      is_verified: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    setUser(mockUser);
    setProfile(mockProfile);
    
    return { data: { user: mockUser }, error: null };
  };

  const signOut = async () => {
    console.log('Demo signout');
    setUser(null);
    setProfile(null);
  };

  const signInWithGoogle = async () => {
    console.log('Demo Google signin');
    return { data: null, error: null };
  };

  const checkSubscription = async () => {
    console.log('Demo subscription check');
  };

  const value = {
    user,
    profile,
    loading,
    signUp,
    signIn,
    signOut,
    signInWithGoogle,
    checkSubscription,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
