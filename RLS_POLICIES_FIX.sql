-- R<PERSON> POLICIES FIX FOR ADVOCATE QUESTION ASSIGNMENT
-- Execute this script in Supabase SQL Editor to fix Row Level Security policies

-- 1. Drop existing conflicting policies
DROP POLICY IF EXISTS "Advocates can view all questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can update questions assigned to them" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can view pending questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can assign questions to themselves" ON public.legal_questions;

-- 2. Create comprehensive policies for advocates

-- Policy 1: Advocates can view all questions (pending and assigned)
CREATE POLICY "Advocates can view all questions" ON public.legal_questions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'advocate' 
      AND is_verified = true
    )
  );

-- Policy 2: Advocates can update questions (for assignment and status changes)
CREATE POLICY "Advocates can update questions" ON public.legal_questions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'advocate' 
      AND is_verified = true
    )
    AND (
      -- Can assign unassigned questions to themselves
      (advocate_id IS NULL AND status = 'pending')
      OR
      -- Can update questions assigned to them
      EXISTS (
        SELECT 1 FROM public.advocates a
        WHERE a.profile_id = auth.uid() 
        AND a.id = advocate_id
      )
    )
  );

-- 3. Ensure advocates table policies allow proper access

-- Drop existing advocate policies
DROP POLICY IF EXISTS "Advocates can manage their own profile" ON public.advocates;
DROP POLICY IF EXISTS "Anyone can view verified advocates" ON public.advocates;

-- Policy for advocates to view all advocate profiles
CREATE POLICY "Advocates can view all advocate profiles" ON public.advocates
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'advocate'
    )
    OR
    EXISTS (
      SELECT 1 FROM public.profiles p
      WHERE p.id = profile_id AND p.is_verified = true
    )
  );

-- Policy for advocates to manage their own profile
CREATE POLICY "Advocates can manage their own profile" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND id = profile_id
    )
  );

-- Policy for creating advocate records (when user becomes advocate)
CREATE POLICY "Users can create advocate profile" ON public.advocates
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND id = profile_id 
      AND role = 'advocate'
    )
  );

-- 4. Ensure responses table policies work correctly

-- Drop existing response policies
DROP POLICY IF EXISTS "Advocates can create responses" ON public.responses;
DROP POLICY IF EXISTS "Users can view responses to their questions" ON public.responses;

-- Policy for advocates to create responses
CREATE POLICY "Advocates can create responses" ON public.responses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() 
      AND a.id = advocate_id
      AND p.role = 'advocate'
      AND p.is_verified = true
    )
  );

-- Policy for viewing responses
CREATE POLICY "Users can view responses" ON public.responses
  FOR SELECT USING (
    -- Users can view responses to their questions
    EXISTS (
      SELECT 1 FROM public.legal_questions lq
      WHERE lq.id = question_id 
      AND lq.user_id = auth.uid()
    )
    OR
    -- Advocates can view their own responses
    EXISTS (
      SELECT 1 FROM public.advocates a
      WHERE a.profile_id = auth.uid() 
      AND a.id = advocate_id
    )
    OR
    -- Admins can view all responses
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Policy for advocates to update their responses
CREATE POLICY "Advocates can update their responses" ON public.responses
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      WHERE a.profile_id = auth.uid() 
      AND a.id = advocate_id
    )
  );

-- 5. Create policy for profiles table to allow advocate role verification

-- Drop existing profile policies that might conflict
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;

-- Policy for users to view their own profile
CREATE POLICY "Users can view their own profile" ON public.profiles
  FOR SELECT USING (id = auth.uid());

-- Policy for users to update their own profile
CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (id = auth.uid());

-- Policy for advocates to view other advocate profiles (for assignment purposes)
CREATE POLICY "Advocates can view other profiles" ON public.profiles
  FOR SELECT USING (
    auth.uid() = id
    OR
    (
      EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() 
        AND role IN ('advocate', 'admin')
      )
      AND role = 'advocate'
      AND is_verified = true
    )
  );

-- 6. Create indexes to improve policy performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_role_verified 
ON public.profiles(role, is_verified) 
WHERE role = 'advocate' AND is_verified = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_advocates_profile_auth 
ON public.advocates(profile_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_legal_questions_assignment 
ON public.legal_questions(advocate_id, status) 
WHERE advocate_id IS NULL OR status IN ('pending', 'assigned');

-- 7. Test the policies with a simple query
-- This will help verify that the policies are working correctly

-- Function to test advocate access
CREATE OR REPLACE FUNCTION public.test_advocate_access(test_advocate_profile_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
  pending_count INTEGER;
  assigned_count INTEGER;
  can_create_advocate BOOLEAN := FALSE;
BEGIN
  -- Test if advocate can see pending questions
  SELECT COUNT(*) INTO pending_count
  FROM public.legal_questions
  WHERE advocate_id IS NULL AND status = 'pending';
  
  -- Test if advocate can see their assigned questions
  SELECT COUNT(*) INTO assigned_count
  FROM public.legal_questions lq
  JOIN public.advocates a ON lq.advocate_id = a.id
  WHERE a.profile_id = test_advocate_profile_id;
  
  -- Test if advocate record can be created
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = test_advocate_profile_id 
    AND role = 'advocate'
  ) INTO can_create_advocate;
  
  result := json_build_object(
    'pending_questions_visible', pending_count,
    'assigned_questions_visible', assigned_count,
    'can_create_advocate_record', can_create_advocate,
    'test_profile_id', test_advocate_profile_id
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.test_advocate_access TO authenticated;

-- 8. Success message
SELECT 
  'RLS policies updated successfully!' as status,
  'Advocates should now be able to assign questions to themselves.' as details,
  'Test the functionality in the advocate interface.' as next_step;
