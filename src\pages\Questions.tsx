
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { MessageCircle, Plus, Clock, CheckCircle } from 'lucide-react';
import { Navigate } from 'react-router-dom';

interface LegalQuestion {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  is_answered: boolean;
  created_at: string;
}

const Questions = () => {
  const { user, profile, loading } = useAuth();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [questions, setQuestions] = useState<LegalQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'general'
  });

  useEffect(() => {
    if (user) {
      fetchQuestions();
    }
  }, [user]);

  const fetchQuestions = async () => {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setQuestions(data || []);
    } catch (error: any) {
      toast({
        title: 'خطأ',
        description: 'فشل في تحميل الأسئلة',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) return;

    try {
      // Check usage limits for free users
      const { data: canCreate, error: limitError } = await supabase
        .rpc('check_usage_limits', {
          user_uuid: user.id,
          action_type: 'question'
        });

      if (limitError) throw limitError;

      if (!canCreate && profile?.subscription_tier === 'free') {
        toast({
          title: 'تم الوصول للحد الأقصى',
          description: 'لقد وصلت للحد الأقصى من الأسئلة هذا الشهر. قم بترقية اشتراكك',
          variant: 'destructive',
        });
        return;
      }

      const { data, error } = await supabase
        .from('legal_questions')
        .insert([
          {
            user_id: user.id,
            title: formData.title,
            description: formData.description,
            category: formData.category
          }
        ])
        .select()
        .single();

      if (error) throw error;

      // Update usage tracking
      if (profile?.subscription_tier === 'free') {
        await supabase
          .from('usage_tracking')
          .update({ 
            questions_this_month: (questions.length + 1)
          })
          .eq('user_id', user.id);
      }

      setQuestions([data, ...questions]);
      setFormData({ title: '', description: '', category: 'general' });
      setShowCreateForm(false);
      
      toast({
        title: 'تم بنجاح',
        description: 'تم إرسال سؤالك بنجاح',
      });
    } catch (error: any) {
      toast({
        title: 'خطأ',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  if (loading && !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/" replace />;
  }

  const categories = [
    { value: 'general', label: 'عام' },
    { value: 'family', label: 'أحوال شخصية' },
    { value: 'commercial', label: 'تجاري' },
    { value: 'criminal', label: 'جنائي' },
    { value: 'civil', label: 'مدني' },
    { value: 'labor', label: 'عمالي' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                الأسئلة القانونية
              </h1>
              <p className="text-lg text-gray-600">
                اطرح أسئلتك القانونية واحصل على إجابات من محامين مختصين
              </p>
            </div>
            <Button 
              onClick={() => setShowCreateForm(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              سؤال جديد
            </Button>
          </div>
        </div>

        {showCreateForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>طرح سؤال جديد</CardTitle>
              <CardDescription>اكتب سؤالك بوضوح للحصول على أفضل إجابة</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">عنوان السؤال</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    placeholder="اكتب عنوان سؤالك"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">تفاصيل السؤال</label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="اشرح سؤالك بالتفصيل"
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">التصنيف</label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    {categories.map((cat) => (
                      <option key={cat.value} value={cat.value}>
                        {cat.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex gap-2">
                  <Button type="submit">إرسال السؤال</Button>
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={() => setShowCreateForm(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>جاري تحميل الأسئلة...</p>
          </div>
        ) : questions.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أسئلة بعد</h3>
              <p className="text-gray-600 mb-4">ابدأ بطرح سؤالك الأول</p>
              <Button onClick={() => setShowCreateForm(true)}>
                طرح سؤال جديد
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {questions.map((question) => (
              <Card key={question.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-xl">{question.title}</CardTitle>
                      <CardDescription className="mt-2">
                        {question.description}
                      </CardDescription>
                    </div>
                    <div className="flex flex-col gap-2 ml-4">
                      <Badge variant={question.is_answered ? "default" : "secondary"}>
                        {question.is_answered ? (
                          <><CheckCircle className="h-3 w-3 mr-1" />تم الرد</>
                        ) : (
                          <><Clock className="h-3 w-3 mr-1" />في الانتظار</>
                        )}
                      </Badge>
                      <Badge variant="outline">
                        {categories.find(c => c.value === question.category)?.label || question.category}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>تاريخ الإرسال: {new Date(question.created_at).toLocaleDateString('ar-EG')}</span>
                    <span>الحالة: {question.status}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Questions;
