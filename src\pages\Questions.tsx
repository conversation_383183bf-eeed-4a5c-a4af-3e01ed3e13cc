
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { QuestionsService, QuestionResponse } from '@/services/questionsService';
import { MessageCircle, Plus, Clock, CheckCircle, User } from 'lucide-react';
import { Navigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  Di<PERSON>Tit<PERSON>,
  DialogTrigger,
} from '@/components/ui/dialog';

interface LegalQuestion {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  is_answered: boolean;
  advocate_id?: string;
  created_at: string;
  updated_at: string;
  user_profile?: {
    full_name: string;
    email: string;
  };
  advocate_profile?: {
    full_name: string;
    email: string;
  };
}

interface Advocate {
  id: string;
  profile_id: string;
  profiles: {
    full_name: string;
    email: string;
  };
}

const Questions = () => {
  const { user, profile, loading } = useAuth();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [questions, setQuestions] = useState<LegalQuestion[]>([]);
  const [advocates, setAdvocates] = useState<Advocate[]>([]);
  const [responses, setResponses] = useState<{ [key: string]: QuestionResponse[] }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<LegalQuestion | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'general',
    advocate_id: ''
  });

  useEffect(() => {
    if (user) {
      fetchQuestions();
      fetchAdvocates();
    }
  }, [user]);

  const fetchQuestions = async () => {
    if (!user) return;

    try {
      const { data, error } = await QuestionsService.getUserQuestions(user.id);
      if (error) throw error;
      setQuestions(data || []);

      // Fetch responses for each question
      if (data) {
        for (const question of data) {
          const { data: responsesData } = await QuestionsService.getQuestionResponses(question.id);
          if (responsesData) {
            setResponses(prev => ({
              ...prev,
              [question.id]: responsesData
            }));
          }
        }
      }
    } catch (error: any) {
      toast({
        title: 'خطأ',
        description: 'فشل في تحميل الأسئلة',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAdvocates = async () => {
    try {
      const { data, error } = await supabase
        .from('advocates')
        .select(`
          id,
          profile_id,
          profiles!advocates_profile_id_fkey(
            full_name,
            email,
            is_verified
          )
        `)
        .eq('profiles.is_verified', true);

      if (error) throw error;
      setAdvocates(data || []);
    } catch (error: any) {
      console.error('Error fetching advocates:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) return;

    try {
      // Create the question
      const { data, error } = await QuestionsService.createQuestion(
        user.id,
        formData.title,
        formData.description,
        formData.category
      );

      if (error) throw error;

      // If an advocate was selected, assign the question
      if (formData.advocate_id && data) {
        await QuestionsService.assignQuestionToAdvocate(data.id, formData.advocate_id);
      }

      setQuestions([data, ...questions]);
      setFormData({ title: '', description: '', category: 'general', advocate_id: '' });
      setShowCreateForm(false);
      
      toast({
        title: 'تم بنجاح',
        description: 'تم إرسال سؤالك بنجاح',
      });
    } catch (error: any) {
      toast({
        title: 'خطأ',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  if (loading && !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/" replace />;
  }

  const categories = [
    { value: 'general', label: 'عام' },
    { value: 'family', label: 'أحوال شخصية' },
    { value: 'commercial', label: 'تجاري' },
    { value: 'criminal', label: 'جنائي' },
    { value: 'civil', label: 'مدني' },
    { value: 'labor', label: 'عمالي' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                الأسئلة القانونية
              </h1>
              <p className="text-lg text-gray-600">
                اطرح أسئلتك القانونية واحصل على إجابات من محامين مختصين
              </p>
            </div>
            <Button 
              onClick={() => setShowCreateForm(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              سؤال جديد
            </Button>
          </div>
        </div>

        {showCreateForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>طرح سؤال جديد</CardTitle>
              <CardDescription>اكتب سؤالك بوضوح للحصول على أفضل إجابة</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">عنوان السؤال</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    placeholder="اكتب عنوان سؤالك"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">تفاصيل السؤال</label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="اشرح سؤالك بالتفصيل"
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">التصنيف</label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    {categories.map((cat) => (
                      <option key={cat.value} value={cat.value}>
                        {cat.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">اختيار محامي (اختياري)</label>
                  <Select
                    value={formData.advocate_id}
                    onValueChange={(value) => setFormData({ ...formData, advocate_id: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر محامي أو اتركه فارغاً للتعيين التلقائي" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">تعيين تلقائي</SelectItem>
                      {advocates.map((advocate) => (
                        <SelectItem key={advocate.profile_id} value={advocate.profile_id}>
                          {advocate.profiles.full_name} - {advocate.profiles.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex gap-2">
                  <Button type="submit">إرسال السؤال</Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowCreateForm(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>جاري تحميل الأسئلة...</p>
          </div>
        ) : questions.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أسئلة بعد</h3>
              <p className="text-gray-600 mb-4">ابدأ بطرح سؤالك الأول</p>
              <Button onClick={() => setShowCreateForm(true)}>
                طرح سؤال جديد
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {questions.map((question) => (
              <Card key={question.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-xl">{question.title}</CardTitle>
                      <CardDescription className="mt-2">
                        {question.description}
                      </CardDescription>
                    </div>
                    <div className="flex flex-col gap-2 ml-4">
                      <Badge variant={question.is_answered ? "default" : "secondary"}>
                        {question.is_answered ? (
                          <><CheckCircle className="h-3 w-3 mr-1" />تم الرد</>
                        ) : (
                          <><Clock className="h-3 w-3 mr-1" />في الانتظار</>
                        )}
                      </Badge>
                      <Badge variant="outline">
                        {categories.find(c => c.value === question.category)?.label || question.category}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                    <span>تاريخ الإرسال: {new Date(question.created_at).toLocaleDateString('ar-EG')}</span>
                    <span>الحالة: {question.status}</span>
                  </div>

                  {/* Display responses */}
                  {responses[question.id] && responses[question.id].length > 0 && (
                    <div className="mt-4 border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-3">الردود:</h4>
                      <div className="space-y-3">
                        {responses[question.id].map((response) => (
                          <div key={response.id} className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-gray-500" />
                                <span className="text-sm font-medium text-gray-700">
                                  {response.advocate_profile?.full_name || 'محامي'}
                                </span>
                              </div>
                              <span className="text-xs text-gray-500">
                                {new Date(response.created_at).toLocaleDateString('ar-EG')}
                              </span>
                            </div>
                            <p className="text-gray-800 leading-relaxed">
                              {response.response_text}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* View responses button */}
                  <div className="mt-4 flex justify-end">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedQuestion(question)}
                        >
                          عرض التفاصيل
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>{selectedQuestion?.title}</DialogTitle>
                          <DialogDescription>
                            {selectedQuestion?.description}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="mt-4">
                          {selectedQuestion && responses[selectedQuestion.id] && (
                            <div className="space-y-4">
                              <h4 className="font-medium">الردود:</h4>
                              {responses[selectedQuestion.id].map((response) => (
                                <div key={response.id} className="border p-4 rounded-lg">
                                  <div className="flex justify-between items-start mb-2">
                                    <span className="font-medium">
                                      {response.advocate_profile?.full_name || 'محامي'}
                                    </span>
                                    <span className="text-sm text-gray-500">
                                      {new Date(response.created_at).toLocaleDateString('ar-EG')}
                                    </span>
                                  </div>
                                  <p className="text-gray-800">{response.response_text}</p>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Questions;
