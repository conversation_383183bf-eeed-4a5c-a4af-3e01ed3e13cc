
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, MessageCircle, Scale, Users } from 'lucide-react';
import { Navigate, Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';

const Dashboard = () => {
  const { user, profile, loading } = useAuth();
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    questions: 0,
    documents: 0,
    advocates: 15,
    consultations: 0
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  useEffect(() => {
    if (user) {
      fetchStats();
    }
  }, [user]);

  const fetchStats = async () => {
    try {
      // Fetch user's questions count
      const { count: questionsCount } = await supabase
        .from('legal_questions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user?.id);

      // Fetch user's documents count
      const { count: documentsCount } = await supabase
        .from('legal_documents')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user?.id);

      // Fetch advocates count (public data)
      const { count: advocatesCount } = await supabase
        .from('advocates')
        .select('*', { count: 'exact', head: true });

      setStats({
        questions: questionsCount || 0,
        documents: documentsCount || 0,
        advocates: advocatesCount || 15,
        consultations: 0 // This would be implemented when consultation system is added
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  if (loading || isLoadingStats) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/" replace />;
  }

  const dashboardCards = [
    {
      title: 'الأسئلة القانونية',
      description: 'اطرح أسئلتك واحصل على إجابات من محامين مختصين',
      icon: MessageCircle,
      count: stats.questions,
      href: '/questions',
    },
    {
      title: 'الوثائق القانونية',
      description: 'أنشئ وأدر الوثائق القانونية الخاصة بك',
      icon: FileText,
      count: stats.documents,
      href: '/documents',
    },
    {
      title: 'المحامون',
      description: 'تصفح شبكة المحامين المعتمدين',
      icon: Users,
      count: stats.advocates,
      href: '/advocates',
    },
    {
      title: 'الاستشارات',
      description: 'احجز جلسات استشارة مع المحامين',
      icon: Scale,
      count: stats.consultations,
      href: '/consultations',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            مرحباً، {profile?.full_name || user.email}
          </h1>
          <p className="text-lg text-gray-600">
            لوحة التحكم الخاصة بك - إدارة احتياجاتك القانونية
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
          {dashboardCards.map((card, index) => (
            <Link key={index} to={card.href}>
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {card.title}
                  </CardTitle>
                  <card.icon className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600 mb-2">{card.count}</div>
                  <CardDescription className="text-xs">
                    {card.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
          <Card>
            <CardHeader>
              <CardTitle>الإجراءات السريعة</CardTitle>
              <CardDescription>ابدأ بأهم المهام القانونية</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Link to="/questions">
                <Button className="w-full justify-start" variant="outline">
                  <MessageCircle className="mr-2 h-4 w-4" />
                  اطرح سؤالاً قانونياً جديداً
                </Button>
              </Link>
              <Link to="/documents">
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  أنشئ وثيقة قانونية
                </Button>
              </Link>
              <Button className="w-full justify-start" variant="outline" disabled>
                <Users className="mr-2 h-4 w-4" />
                ابحث عن محامي (قريباً)
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>حالة الاشتراك</CardTitle>
              <CardDescription>معلومات خطة الاشتراك الحالية</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">الخطة الحالية</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                    {profile?.subscription_tier === 'free' ? 'مجاني' : 
                     profile?.subscription_tier === 'pro_user' ? 'مستخدم متميز' : 'محامي متميز'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">الأسئلة المتبقية</span>
                  <span className="text-sm text-gray-600">
                    {profile?.subscription_tier === 'free' ? 
                      `${Math.max(0, 1 - stats.questions)} من 1` : 'غير محدود'}
                  </span>
                </div>
                <Link to="/#pricing">
                  <Button className="w-full" size="sm">
                    ترقية الاشتراك
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
