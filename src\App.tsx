
import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/AuthContext';
import { Header } from '@/components/layout/Header';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import Index from '@/pages/Index';
import Dashboard from '@/pages/Dashboard';
import AdvocateDashboard from '@/pages/AdvocateDashboard';
import Questions from '@/pages/Questions';
import Documents from '@/pages/Documents';
import Chatbot from '@/pages/Chatbot';
import Admin from '@/pages/Admin';
import Debug from '@/pages/Debug';
import SimpleTest from '@/pages/SimpleTest';
import TestConnection from '@/pages/TestConnection';
import NotFound from '@/pages/NotFound';
import './lib/i18n';
import './App.css';

const queryClient = new QueryClient();

function App() {
  useEffect(() => {
    // Set initial direction based on default language (Arabic)
    document.dir = 'rtl';
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <Router>
            <div className="min-h-screen bg-gray-50 w-full">
              <Header />
              <main className="w-full min-h-screen">
                <ErrorBoundary>
                  <Routes>
                    <Route path="/" element={<Index />} />
                    <Route
                      path="/dashboard"
                      element={
                        <ProtectedRoute requiredRole="user">
                          <Dashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/advocate-dashboard"
                      element={
                        <ProtectedRoute requiredRole="advocate" requireVerification={true}>
                          <AdvocateDashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/questions"
                      element={
                        <ProtectedRoute>
                          <Questions />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/documents"
                      element={
                        <ProtectedRoute>
                          <Documents />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/chatbot"
                      element={
                        <ProtectedRoute>
                          <Chatbot />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin"
                      element={
                        <ProtectedRoute requiredRole="admin">
                          <Admin />
                        </ProtectedRoute>
                      }
                    />
                    <Route path="/debug" element={<Debug />} />
                    <Route path="/test" element={<SimpleTest />} />
                    <Route path="/test-connection" element={<TestConnection />} />
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </ErrorBoundary>
              </main>
              <Toaster />
            </div>
          </Router>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
