
import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/AuthContext';
import { Header } from '@/components/layout/Header';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import Index from '@/pages/Index';
import Dashboard from '@/pages/Dashboard';
import Questions from '@/pages/Questions';
import Documents from '@/pages/Documents';
import Chatbot from '@/pages/Chatbot';
import Admin from '@/pages/Admin';
import NotFound from '@/pages/NotFound';
import './lib/i18n';
import './App.css';

const queryClient = new QueryClient();

function App() {
  useEffect(() => {
    // Set initial direction based on default language (Arabic)
    document.dir = 'rtl';
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <Router>
            <div className="min-h-screen bg-gray-50">
              <Header />
              <main className="w-full">
                <ErrorBoundary>
                  <Routes>
                    <Route path="/" element={<Index />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/questions" element={<Questions />} />
                    <Route path="/documents" element={<Documents />} />
                    <Route path="/chatbot" element={<Chatbot />} />
                    {/* <Route path="/admin" element={<Admin />} /> */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </ErrorBoundary>
              </main>
              <Toaster />
            </div>
          </Router>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
