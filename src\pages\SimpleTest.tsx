import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

const SimpleTest = () => {
  const { user, profile, loading, signIn, signUp, signOut } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [fullName, setFullName] = useState('Test User');

  const handleSignIn = async () => {
    try {
      const result = await signIn(email, password);
      console.log('SignIn result:', result);
    } catch (error) {
      console.error('SignIn error:', error);
    }
  };

  const handleSignUp = async () => {
    try {
      const result = await signUp(email, password, fullName);
      console.log('SignUp result:', result);
    } catch (error) {
      console.error('SignUp error:', error);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      console.log('SignOut successful');
    } catch (error) {
      console.error('SignOut error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Simple Auth Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Current Status</h3>
              <div className="flex gap-2">
                <Badge variant={loading ? "destructive" : "default"}>
                  {loading ? "Loading..." : "Ready"}
                </Badge>
                <Badge variant={user ? "default" : "secondary"}>
                  {user ? "Authenticated" : "Not Authenticated"}
                </Badge>
              </div>
            </div>

            {!user ? (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleSignIn}>Sign In</Button>
                  <Button onClick={handleSignUp} variant="outline">Sign Up</Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">User Info</h3>
                  <pre className="text-xs bg-gray-100 p-2 rounded">
                    {JSON.stringify(user, null, 2)}
                  </pre>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Profile Info</h3>
                  <pre className="text-xs bg-gray-100 p-2 rounded">
                    {JSON.stringify(profile, null, 2)}
                  </pre>
                </div>
                <Button onClick={handleSignOut} variant="destructive">
                  Sign Out
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Navigation Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button asChild className="w-full">
                <a href="/">Home</a>
              </Button>
              <Button asChild className="w-full" variant="outline">
                <a href="/debug">Debug Page</a>
              </Button>
              {user && (
                <>
                  <Button asChild className="w-full" variant="outline">
                    <a href="/dashboard">Dashboard</a>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <a href="/questions">Questions</a>
                  </Button>
                  <Button asChild className="w-full" variant="outline">
                    <a href="/chatbot">Chatbot</a>
                  </Button>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SimpleTest;
