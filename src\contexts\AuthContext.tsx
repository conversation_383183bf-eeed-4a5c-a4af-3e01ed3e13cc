
import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

interface Profile {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: 'admin' | 'user' | 'advocate';
  subscription_tier: 'free' | 'pro_user' | 'pro_advocate';
  subscription_end?: string;
  trial_end?: string;
  stripe_customer_id?: string;
  is_verified?: boolean;
}

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName: string, role?: 'user' | 'advocate') => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<any>;
  checkSubscription: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    // Force stop loading after 5 seconds to prevent infinite loading
    const forceStopLoading = setTimeout(() => {
      if (mounted && loading) {
        console.log('Force stopping loading after timeout');
        setLoading(false);
        setInitialized(true);
      }
    }, 5000);

    const initializeAuth = async () => {
      try {
        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (!mounted) return;

        console.log('Initial session:', session);

        if (error) {
          console.error('Session error:', error);
          setLoading(false);
          setInitialized(true);
          return;
        }

        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchProfile(session.user.id);
        } else {
          setLoading(false);
          setInitialized(true);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          setLoading(false);
          setInitialized(true);
        }
      }
    };

    // Initialize auth
    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return;

      console.log('Auth state changed:', event, session);
      setUser(session?.user ?? null);

      if (session?.user) {
        await fetchProfile(session.user.id);
      } else {
        setProfile(null);
        setLoading(false);
        setInitialized(true);
      }
    });

    return () => {
      mounted = false;
      clearTimeout(forceStopLoading);
      subscription.unsubscribe();
    };
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      console.log('Fetching profile for user:', userId);

      // Set a timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Profile fetch timeout')), 10000)
      );

      const fetchPromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      const { data, error } = await Promise.race([fetchPromise, timeoutPromise]) as any;

      if (error) {
        console.error('Error fetching profile:', error);
        // If profile doesn't exist, create a basic one
        if (error.code === 'PGRST116' || error.message === 'Profile fetch timeout') {
          console.log('Profile not found or timeout, creating basic profile');
          const basicProfile = {
            id: userId,
            email: '',
            role: 'user' as const,
            subscription_tier: 'free' as const,
            is_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          setProfile(basicProfile);
        } else {
          // For other errors, still set a basic profile to avoid infinite loading
          setProfile({
            id: userId,
            email: '',
            role: 'user' as const,
            subscription_tier: 'free' as const,
            is_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        }
      } else {
        console.log('Profile fetched:', data);
        setProfile(data);
      }
    } catch (error) {
      console.error('Error in fetchProfile:', error);
      // Always set a basic profile to prevent infinite loading
      setProfile({
        id: userId,
        email: '',
        role: 'user' as const,
        subscription_tier: 'free' as const,
        is_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    } finally {
      setLoading(false);
      setInitialized(true);
    }
  };

  const checkSubscription = async () => {
    try {
      console.log('Checking subscription status...');
      // For now, we'll skip the subscription check since the function might not exist
      // This can be implemented later when the Supabase function is ready
      console.log('Subscription check skipped - function not implemented');
    } catch (error) {
      console.error('Error checking subscription:', error);
    }
  };

  const signUp = async (email: string, password: string, fullName: string, role: 'user' | 'advocate' = 'user') => {
    console.log('Signing up user:', email, 'with role:', role);

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            role: role,
          },
          emailRedirectTo: `${window.location.origin}/`,
        },
      });

      if (error) {
        console.error('Signup error:', error);
        return { data, error };
      }

      if (data.user) {
        // Update the profile with the selected role
        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            role: role,
            full_name: fullName,
            is_verified: role === 'user' // Users are auto-verified, advocates need admin approval
          })
          .eq('id', data.user.id);

        if (profileError) {
          console.error('Error updating profile:', profileError);
        }

        // If role is advocate, create advocate profile
        if (role === 'advocate') {
          const { error: advocateError } = await supabase
            .from('advocates')
            .insert({
              profile_id: data.user.id,
              specializations: [],
              bio: '',
              hourly_rate: 0,
              is_featured: false,
              rating: 0,
              total_reviews: 0,
              availability: {}
            });

          if (advocateError) {
            console.error('Error creating advocate profile:', advocateError);
          }
        }
      }

      console.log('Signup successful:', data);
      return { data, error: null };
    } catch (error) {
      console.error('Signup error:', error);
      return { data: null, error };
    }
  };

  const signIn = async (email: string, password: string) => {
    console.log('Signing in user:', email);
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error('Signin error:', error);
    } else {
      console.log('Signin successful:', data);
    }
    
    return { data, error };
  };

  const signOut = async () => {
    console.log('Signing out user');
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Signout error:', error);
        throw error;
      }
      // Clear local state
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error('Error during signout:', error);
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    console.log('Signing in with Google');
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/`,
      },
    });
    
    if (error) {
      console.error('Google signin error:', error);
    } else {
      console.log('Google signin initiated:', data);
    }
    
    return { data, error };
  };

  const value = {
    user,
    profile,
    loading,
    signUp,
    signIn,
    signOut,
    signInWithGoogle,
    checkSubscription, // Add this to the context
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
