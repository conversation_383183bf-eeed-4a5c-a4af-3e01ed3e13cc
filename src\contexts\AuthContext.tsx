
import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

interface Profile {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  role: 'admin' | 'user' | 'advocate';
  subscription_tier: 'free' | 'pro_user' | 'pro_advocate';
  subscription_end?: string;
  trial_end?: string;
  stripe_customer_id?: string;
  is_verified?: boolean;
}

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName: string, role?: 'user' | 'advocate') => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<any>;
  checkSubscription: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('Initial session:', session);
      setUser(session?.user ?? null);
      if (session?.user) {
        fetchProfile(session.user.id);
        // Skip subscription check for now
        // checkSubscription();
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session);
      setUser(session?.user ?? null);
      if (session?.user) {
        await fetchProfile(session.user.id);
        // Skip subscription check for now
        // await checkSubscription();
      } else {
        setProfile(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      console.log('Fetching profile for user:', userId);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        // If profile doesn't exist, create a basic one
        if (error.code === 'PGRST116') {
          console.log('Profile not found, creating basic profile');
          setProfile({
            id: userId,
            email: '',
            role: 'user',
            subscription_tier: 'free',
            is_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        } else {
          setProfile(null);
        }
      } else {
        console.log('Profile fetched:', data);
        setProfile(data);
      }
    } catch (error) {
      console.error('Error in fetchProfile:', error);
      setProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const checkSubscription = async () => {
    try {
      console.log('Checking subscription status...');
      // For now, we'll skip the subscription check since the function might not exist
      // This can be implemented later when the Supabase function is ready
      console.log('Subscription check skipped - function not implemented');
    } catch (error) {
      console.error('Error checking subscription:', error);
    }
  };

  const signUp = async (email: string, password: string, fullName: string, role: 'user' | 'advocate' = 'user') => {
    console.log('Signing up user:', email, 'with role:', role);

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            role: role,
          },
          emailRedirectTo: `${window.location.origin}/`,
        },
      });

      if (error) {
        console.error('Signup error:', error);
        return { data, error };
      }

      if (data.user) {
        // Update the profile with the selected role
        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            role: role,
            full_name: fullName,
            is_verified: role === 'user' // Users are auto-verified, advocates need admin approval
          })
          .eq('id', data.user.id);

        if (profileError) {
          console.error('Error updating profile:', profileError);
        }

        // If role is advocate, create advocate profile
        if (role === 'advocate') {
          const { error: advocateError } = await supabase
            .from('advocates')
            .insert({
              profile_id: data.user.id,
              specializations: [],
              bio: '',
              hourly_rate: 0,
              is_featured: false,
              rating: 0,
              total_reviews: 0,
              availability: {}
            });

          if (advocateError) {
            console.error('Error creating advocate profile:', advocateError);
          }
        }
      }

      console.log('Signup successful:', data);
      return { data, error: null };
    } catch (error) {
      console.error('Signup error:', error);
      return { data: null, error };
    }
  };

  const signIn = async (email: string, password: string) => {
    console.log('Signing in user:', email);
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error('Signin error:', error);
    } else {
      console.log('Signin successful:', data);
    }
    
    return { data, error };
  };

  const signOut = async () => {
    console.log('Signing out user');
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Signout error:', error);
        throw error;
      }
      // Clear local state
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error('Error during signout:', error);
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    console.log('Signing in with Google');
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/`,
      },
    });
    
    if (error) {
      console.error('Google signin error:', error);
    } else {
      console.log('Google signin initiated:', data);
    }
    
    return { data, error };
  };

  const value = {
    user,
    profile,
    loading,
    signUp,
    signIn,
    signOut,
    signInWithGoogle,
    checkSubscription, // Add this to the context
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
