import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { AdminService, UserProfile, UpdateUserData } from '@/services/adminService';
import { Edit, Loader2, X, Save } from 'lucide-react';

interface UserEditFormProps {
  user: UserProfile;
  onUserUpdated: () => void;
  onClose: () => void;
}

const SPECIALIZATIONS = [
  'family', 'civil', 'commercial', 'criminal', 'labor', 'real_estate', 
  'tax', 'immigration', 'intellectual_property', 'environmental'
];

const SPECIALIZATION_LABELS: Record<string, string> = {
  family: 'قانون الأسرة',
  civil: 'القانون المدني',
  commercial: 'القانون التجاري',
  criminal: 'القانون الجنائي',
  labor: 'قانون العمل',
  real_estate: 'قانون العقارات',
  tax: 'القانون الضريبي',
  immigration: 'قانون الهجرة',
  intellectual_property: 'الملكية الفكرية',
  environmental: 'القانون البيئي'
};

export const UserEditForm: React.FC<UserEditFormProps> = ({ user, onUserUpdated, onClose }) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<UpdateUserData>({
    name: user.full_name || '',
    phone: user.phone || '',
    role: user.role,
    subscription_tier: user.subscription_tier,
    is_verified: user.is_verified || false,
    advocate_specializations: [],
    advocate_bio: '',
    advocate_hourly_rate: 500
  });

  // Load advocate data if user is an advocate
  useEffect(() => {
    if (user.role === 'advocate' && (user as any).advocates) {
      const advocateData = (user as any).advocates[0];
      if (advocateData) {
        setFormData(prev => ({
          ...prev,
          advocate_specializations: advocateData.specializations || [],
          advocate_bio: advocateData.bio || '',
          advocate_hourly_rate: advocateData.hourly_rate || 500
        }));
      }
    }
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { data, error } = await AdminService.updateUser(user.id, formData);

      if (error) {
        throw error;
      }

      if (data?.success) {
        toast({
          title: 'تم تحديث المستخدم',
          description: `تم تحديث بيانات ${user.email} بنجاح`,
        });
        onUserUpdated();
        onClose();
      } else {
        throw new Error(data?.message || 'فشل في تحديث المستخدم');
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في تحديث المستخدم',
        description: error.message || 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSpecializationToggle = (specialization: string) => {
    const current = formData.advocate_specializations || [];
    const updated = current.includes(specialization)
      ? current.filter(s => s !== specialization)
      : [...current, specialization];
    
    setFormData({ ...formData, advocate_specializations: updated });
  };

  return (
    <Card className="w-full mx-auto bg-white shadow-lg">
      <CardHeader className="sticky top-0 bg-white border-b z-10">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              تعديل المستخدم
            </CardTitle>
            <CardDescription>
              تعديل بيانات {user.email}
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="max-h-[calc(100vh-200px)] overflow-y-auto">
        <form onSubmit={handleSubmit} className="space-y-6 py-4">
          {/* User Info Display */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">معلومات الحساب</span>
              <Badge variant="outline">{user.email}</Badge>
            </div>
            <div className="text-sm text-gray-600">
              تاريخ الإنشاء: {new Date(user.created_at || '').toLocaleDateString('ar-EG')}
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">الاسم الكامل</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="الاسم الكامل"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">رقم الهاتف</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="+212 6XX XXX XXX"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="role">نوع الحساب</Label>
              <Select
                value={formData.role}
                onValueChange={(value: 'user' | 'advocate' | 'admin') => 
                  setFormData({ ...formData, role: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">مستخدم عادي</SelectItem>
                  <SelectItem value="advocate">محامي</SelectItem>
                  <SelectItem value="admin">مدير</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="subscription">نوع الاشتراك</Label>
              <Select
                value={formData.subscription_tier}
                onValueChange={(value: 'free' | 'pro_user' | 'pro_advocate') => 
                  setFormData({ ...formData, subscription_tier: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="free">مجاني</SelectItem>
                  <SelectItem value="pro_user">مستخدم متميز</SelectItem>
                  <SelectItem value="pro_advocate">محامي متميز</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="verified"
              checked={formData.is_verified}
              onCheckedChange={(checked) => 
                setFormData({ ...formData, is_verified: checked })
              }
            />
            <Label htmlFor="verified">حساب مفعل</Label>
          </div>

          {/* Advocate-specific fields */}
          {formData.role === 'advocate' && (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
              <h3 className="font-medium text-lg">معلومات المحامي</h3>
              
              <div className="space-y-2">
                <Label>التخصصات</Label>
                <div className="flex flex-wrap gap-2">
                  {SPECIALIZATIONS.map((spec) => (
                    <Badge
                      key={spec}
                      variant={formData.advocate_specializations?.includes(spec) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => handleSpecializationToggle(spec)}
                    >
                      {SPECIALIZATION_LABELS[spec]}
                    </Badge>
                  ))}
                </div>
                <div className="text-sm text-gray-600">
                  اختر التخصصات بالنقر عليها
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">نبذة تعريفية</Label>
                <Textarea
                  id="bio"
                  value={formData.advocate_bio}
                  onChange={(e) => setFormData({ ...formData, advocate_bio: e.target.value })}
                  placeholder="نبذة مختصرة عن المحامي وخبراته..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="hourly_rate">السعر بالساعة (درهم)</Label>
                <Input
                  id="hourly_rate"
                  type="number"
                  value={formData.advocate_hourly_rate}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    advocate_hourly_rate: parseFloat(e.target.value) || 500 
                  })}
                  min="0"
                  step="50"
                />
              </div>
            </div>
          )}

          {/* Submit buttons */}
          <div className="flex justify-end gap-3 pt-4 sticky bottom-0 bg-white border-t -mx-6 px-6 py-4 mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              إلغاء
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  حفظ التغييرات
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
