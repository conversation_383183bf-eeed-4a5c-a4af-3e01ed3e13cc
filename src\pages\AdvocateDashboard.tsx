import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { QuestionsService, LegalQuestion } from '@/services/questionsService';
import { AuthService, AdvocateProfile } from '@/services/authService';
import { 
  MessageCircle, 
  Clock, 
  CheckCircle, 
  User, 
  Calendar,
  Star,
  DollarSign,
  Settings
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const AdvocateDashboard = () => {
  const { t } = useTranslation();
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const [questions, setQuestions] = useState<LegalQuestion[]>([]);
  const [pendingQuestions, setPendingQuestions] = useState<LegalQuestion[]>([]);
  const [advocateProfile, setAdvocateProfile] = useState<AdvocateProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedQuestion, setSelectedQuestion] = useState<LegalQuestion | null>(null);
  const [answer, setAnswer] = useState('');
  const [isAnswering, setIsAnswering] = useState(false);

  useEffect(() => {
    if (user && profile?.role === 'advocate') {
      loadAdvocateData();
    }
  }, [user, profile]);

  const loadAdvocateData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Load advocate profile
      const advocateData = await AuthService.getAdvocateProfile(user.id);
      setAdvocateProfile(advocateData);

      // Load assigned questions
      const { data: assignedQuestions } = await QuestionsService.getAdvocateQuestions(user.id);
      setQuestions(assignedQuestions || []);

      // Load pending questions (available to take)
      const { data: pendingQuestionsData } = await QuestionsService.getPendingQuestions();
      setPendingQuestions(pendingQuestionsData || []);

    } catch (error) {
      console.error('Error loading advocate data:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تحميل البيانات',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTakeQuestion = async (questionId: string) => {
    if (!user) return;

    try {
      const { error } = await QuestionsService.assignQuestionToAdvocate(questionId, user.id);
      
      if (error) throw error;

      toast({
        title: 'تم بنجاح',
        description: 'تم تعيين السؤال لك بنجاح',
      });

      // Reload data
      loadAdvocateData();
    } catch (error) {
      console.error('Error taking question:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تعيين السؤال',
        variant: 'destructive',
      });
    }
  };

  const handleAnswerQuestion = async () => {
    if (!selectedQuestion || !answer.trim() || !user) return;

    try {
      setIsAnswering(true);
      
      const { error } = await QuestionsService.answerQuestion(
        selectedQuestion.id, 
        answer, 
        user.id
      );
      
      if (error) throw error;

      toast({
        title: 'تم بنجاح',
        description: 'تم إرسال الإجابة بنجاح',
      });

      setSelectedQuestion(null);
      setAnswer('');
      loadAdvocateData();
    } catch (error) {
      console.error('Error answering question:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في إرسال الإجابة',
        variant: 'destructive',
      });
    } finally {
      setIsAnswering(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!profile || profile.role !== 'advocate') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">غير مصرح</CardTitle>
            <CardDescription className="text-center">
              هذه الصفحة مخصصة للمحامين فقط
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (!profile.is_verified) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">في انتظار التفعيل</CardTitle>
            <CardDescription className="text-center">
              حسابك قيد المراجعة من قبل الإدارة. سيتم تفعيله قريباً.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const stats = {
    totalQuestions: questions.length,
    answeredQuestions: questions.filter(q => q.is_answered).length,
    pendingQuestions: questions.filter(q => !q.is_answered).length,
    rating: advocateProfile?.rating || 0,
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">لوحة تحكم المحامي</h1>
          <p className="text-muted-foreground">مرحباً {profile.full_name}</p>
        </div>
        <Button variant="outline">
          <Settings className="w-4 h-4 mr-2" />
          إعدادات الملف الشخصي
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الأسئلة</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalQuestions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">تم الإجابة عليها</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.answeredQuestions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">في الانتظار</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingQuestions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">التقييم</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.rating.toFixed(1)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Questions Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Questions */}
        <Card>
          <CardHeader>
            <CardTitle>أسئلتي المعينة</CardTitle>
            <CardDescription>الأسئلة المعينة لك للإجابة عليها</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {questions.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">
                لا توجد أسئلة معينة لك حالياً
              </p>
            ) : (
              questions.map((question) => (
                <div key={question.id} className="border rounded-lg p-4 space-y-2">
                  <div className="flex justify-between items-start">
                    <h4 className="font-medium">{question.title}</h4>
                    <Badge variant={question.is_answered ? "default" : "secondary"}>
                      {question.is_answered ? "تم الرد" : "في الانتظار"}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {question.description}
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground">
                      {new Date(question.created_at).toLocaleDateString('ar')}
                    </span>
                    {!question.is_answered && (
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            size="sm" 
                            onClick={() => setSelectedQuestion(question)}
                          >
                            الإجابة
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>{selectedQuestion?.title}</DialogTitle>
                            <DialogDescription>
                              {selectedQuestion?.description}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="answer">إجابتك</Label>
                              <Textarea
                                id="answer"
                                value={answer}
                                onChange={(e) => setAnswer(e.target.value)}
                                placeholder="اكتب إجابتك هنا..."
                                rows={6}
                              />
                            </div>
                            <div className="flex justify-end space-x-2">
                              <Button
                                onClick={handleAnswerQuestion}
                                disabled={!answer.trim() || isAnswering}
                              >
                                {isAnswering ? 'جاري الإرسال...' : 'إرسال الإجابة'}
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    )}
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Available Questions */}
        <Card>
          <CardHeader>
            <CardTitle>الأسئلة المتاحة</CardTitle>
            <CardDescription>أسئلة يمكنك أخذها والإجابة عليها</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {pendingQuestions.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">
                لا توجد أسئلة متاحة حالياً
              </p>
            ) : (
              pendingQuestions.map((question) => (
                <div key={question.id} className="border rounded-lg p-4 space-y-2">
                  <h4 className="font-medium">{question.title}</h4>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {question.description}
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-muted-foreground">
                      {new Date(question.created_at).toLocaleDateString('ar')}
                    </span>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleTakeQuestion(question.id)}
                    >
                      أخذ السؤال
                    </Button>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdvocateDashboard;
