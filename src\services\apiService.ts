import axios from 'axios';

export interface Document {
  id: string;
  name: string;
}

export const API_URL = 'https://benzaid.app.n8n.cloud/webhook/2e1dd36d-1458-4242-9462-0adfe5b12b1f';

export const fetchDocuments = async (): Promise<Document[]> => {
  try {
    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching documents:', error);
    throw error;
  }
};

export const downloadDocument = async (id: string, filename: string) => {
  // Note: You'll need to implement the actual download URL for the documents
  const downloadUrl = `YOUR_DOWNLOAD_URL/${id}`;
  
  try {
    const response = await axios.get(downloadUrl, {
      responseType: 'blob',
    });
    
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
  } catch (error) {
    console.error('Error downloading document:', error);
    throw error;
  }
};
