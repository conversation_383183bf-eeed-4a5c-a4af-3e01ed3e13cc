-- Create responses table for storing answers to legal questions
CREATE TABLE public.responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id UUID NOT NULL REFERENCES public.legal_questions(id) ON DELETE CASCADE,
  advocate_id UUID NOT NULL REFERENCES public.advocates(id) ON DELETE CASCADE,
  response_text TEXT NOT NULL,
  is_approved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Enable Row Level Security for responses
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for responses
CREATE POLICY "Users can view responses to their questions" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.legal_questions lq
      WHERE lq.id = question_id AND lq.user_id = auth.uid()
    )
  );

CREATE POLICY "Advocates can view their own responses" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Advocates can create responses" ON public.responses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Advocates can update their own responses" ON public.responses
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Admins can manage all responses" ON public.responses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Fix existing RLS policies for better admin and advocate access

-- Drop existing policies that might be too restrictive
DROP POLICY IF EXISTS "Advocates can view assigned questions" ON public.legal_questions;
DROP POLICY IF EXISTS "Advocates can update their own profile" ON public.advocates;

-- Create better policies for legal_questions
CREATE POLICY "Advocates can view all questions" ON public.legal_questions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'advocate' AND is_verified = true
    )
  );

CREATE POLICY "Advocates can update questions assigned to them" ON public.legal_questions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.advocates a
      JOIN public.profiles p ON a.profile_id = p.id
      WHERE p.id = auth.uid() AND a.id = advocate_id
    )
  );

CREATE POLICY "Admins can manage all questions" ON public.legal_questions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create better policies for advocates table
CREATE POLICY "Advocates can manage their own profile" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND id = profile_id
    )
  );

CREATE POLICY "Admins can manage all advocates" ON public.advocates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_responses_question_id ON public.responses(question_id);
CREATE INDEX IF NOT EXISTS idx_responses_advocate_id ON public.responses(advocate_id);
CREATE INDEX IF NOT EXISTS idx_legal_questions_advocate_id ON public.legal_questions(advocate_id);
CREATE INDEX IF NOT EXISTS idx_legal_questions_user_id ON public.legal_questions(user_id);
CREATE INDEX IF NOT EXISTS idx_advocates_profile_id ON public.advocates(profile_id);

-- Create function to automatically update question status when response is added
CREATE OR REPLACE FUNCTION public.update_question_status_on_response()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the question to mark it as answered
  UPDATE public.legal_questions
  SET 
    is_answered = true,
    status = 'answered',
    updated_at = now()
  WHERE id = NEW.question_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update question status
CREATE TRIGGER on_response_created
  AFTER INSERT ON public.responses
  FOR EACH ROW EXECUTE PROCEDURE public.update_question_status_on_response();

-- Create function to assign question to advocate
CREATE OR REPLACE FUNCTION public.assign_question_to_advocate(
  question_uuid UUID,
  advocate_profile_uuid UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  advocate_uuid UUID;
BEGIN
  -- Get advocate ID from profile ID
  SELECT id INTO advocate_uuid
  FROM public.advocates
  WHERE profile_id = advocate_profile_uuid;
  
  IF advocate_uuid IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Update the question
  UPDATE public.legal_questions
  SET 
    advocate_id = advocate_uuid,
    status = 'assigned',
    updated_at = now()
  WHERE id = question_uuid;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
