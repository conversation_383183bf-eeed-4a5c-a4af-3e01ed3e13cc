
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

const resources = {
  ar: {
    translation: {
      // Navigation
      'nav.home': 'الرئيسية',
      'nav.pricing': 'الأسعار',
      'nav.advocates': 'المحامون',
      'nav.documents': 'الوثائق',
      'nav.questions': 'الأسئلة',
      'nav.dashboard': 'لوحة التحكم',
      'nav.chatbot': 'المساعد القانوني',
      'nav.admin': 'إدارة النظام',
      'nav.advocate_dashboard': 'لوحة تحكم المحامي',
      'nav.login': 'تسجيل الدخول',
      'nav.signup': 'إنشاء حساب',
      'nav.logout': 'تسجيل الخروج',
      
      // Hero Section
      'hero.title': 'المساعدة القانونية المغربية',
      'hero.subtitle': 'مساعدك القانوني الرقمي للمواطنين والمحامين المغاربة',
      'hero.description': 'احصل على استشارات قانونية من محامين معتمدين، وأنشئ الوثائق القانونية، واطرح الأسئلة القانونية بسهولة',
      'hero.cta.primary': 'ابدأ الآن مجاناً',
      'hero.cta.secondary': 'تعرف على المزيد',
      
      // Pricing
      'pricing.title': 'خطط الأسعار',
      'pricing.subtitle': 'اختر الخطة المناسبة لاحتياجاتك القانونية',
      
      // Free Plan
      'pricing.free.title': 'مجاني',
      'pricing.free.price': '0 درهم/شهر',
      'pricing.free.features.questions': 'سؤال قانوني واحد شهرياً',
      'pricing.free.features.documents': 'إنشاء وثائق محدود',
      'pricing.free.features.trial': 'تجربة مجانية 7 أيام للباقة المتميزة',
      'pricing.free.cta': 'ابدأ مجاناً',
      
      // Pro User Plan
      'pricing.pro.title': 'المستخدم المتميز',
      'pricing.pro.price': '100 درهم/شهر',
      'pricing.pro.features.questions': 'أسئلة قانونية غير محدودة',
      'pricing.pro.features.documents': 'إنشاء وثائق غير محدود',
      'pricing.pro.features.support': 'دعم ذو أولوية',
      'pricing.pro.features.advocates': 'وصول كامل للمحامين',
      'pricing.pro.cta': 'اشترك الآن',
      
      // Pro Advocate Plan
      'pricing.advocate.title': 'المحامي المتميز',
      'pricing.advocate.price': '500 درهم/شهر',
      'pricing.advocate.features.featured': 'ملف شخصي مميز في البحث',
      'pricing.advocate.features.booking': 'أدوات الحجز والتحليلات',
      'pricing.advocate.features.availability': 'تحديد الأوقات والأسعار',
      'pricing.advocate.features.priority': 'أولوية في عرض الملف الشخصي',
      'pricing.advocate.cta': 'اشترك كمحامي',
      
      // Authentication
      'auth.signin.title': 'تسجيل الدخول',
      'auth.signin.subtitle': 'أدخل بياناتك للوصول إلى حسابك',
      'auth.signup.title': 'إنشاء حساب جديد',
      'auth.signup.subtitle': 'أنشئ حساباً جديداً للبدء',
      'auth.email': 'البريد الإلكتروني',
      'auth.password': 'كلمة المرور',
      'auth.fullname': 'الاسم الكامل',
      'auth.signin.button': 'تسجيل الدخول',
      'auth.signup.button': 'إنشاء حساب',
      'auth.google': 'متابعة مع Google',
      'auth.switch.signin': 'لديك حساب؟ سجل دخولك',
      'auth.switch.signup': 'ليس لديك حساب؟ أنشئ حساباً جديداً',
      
      // Common
      'common.loading': 'جاري التحميل...',
      'common.error': 'حدث خطأ',
      'common.success': 'تم بنجاح',
      'common.cancel': 'إلغاء',
      'common.save': 'حفظ',
      'common.edit': 'تعديل',
      'common.delete': 'حذف',
    },
  },
  fr: {
    translation: {
      // Navigation
      'nav.home': 'Accueil',
      'nav.pricing': 'Tarifs',
      'nav.advocates': 'Avocats',
      'nav.documents': 'Documents',
      'nav.questions': 'Questions',
      'nav.dashboard': 'Tableau de bord',
      'nav.chatbot': 'Assistant Juridique',
      'nav.admin': 'Administration',
      'nav.advocate_dashboard': 'Tableau de bord Avocat',
      'nav.login': 'Connexion',
      'nav.signup': 'Inscription',
      'nav.logout': 'Déconnexion',
      
      // Hero Section
      'hero.title': 'Aide Juridique Marocaine',
      'hero.subtitle': 'Votre assistant juridique numérique pour les citoyens et avocats marocains',
      'hero.description': 'Obtenez des conseils juridiques d\'avocats vérifiés, créez des documents légaux et posez des questions juridiques facilement',
      'hero.cta.primary': 'Commencer gratuitement',
      'hero.cta.secondary': 'En savoir plus',
      
      // Pricing
      'pricing.title': 'Plans tarifaires',
      'pricing.subtitle': 'Choisissez le plan qui correspond à vos besoins juridiques',
      
      // Free Plan
      'pricing.free.title': 'Gratuit',
      'pricing.free.price': '0 MAD/mois',
      'pricing.free.features.questions': '1 question juridique par mois',
      'pricing.free.features.documents': 'Génération de documents limitée',
      'pricing.free.features.trial': 'Essai gratuit de 7 jours Pro',
      'pricing.free.cta': 'Commencer gratuitement',
      
      // Pro User Plan
      'pricing.pro.title': 'Utilisateur Pro',
      'pricing.pro.price': '100 MAD/mois',
      'pricing.pro.features.questions': 'Questions juridiques illimitées',
      'pricing.pro.features.documents': 'Génération de documents illimitée',
      'pricing.pro.features.support': 'Support prioritaire',
      'pricing.pro.features.advocates': 'Accès complet aux avocats',
      'pricing.pro.cta': 'S\'abonner maintenant',
      
      // Pro Advocate Plan
      'pricing.advocate.title': 'Avocat Pro',
      'pricing.advocate.price': '500 MAD/mois',
      'pricing.advocate.features.featured': 'Profil mis en avant dans la recherche',
      'pricing.advocate.features.booking': 'Outils de réservation et analytics',
      'pricing.advocate.features.availability': 'Définir disponibilités et tarifs',
      'pricing.advocate.features.priority': 'Priorité d\'affichage du profil',
      'pricing.advocate.cta': 'S\'abonner comme avocat',
      
      // Authentication
      'auth.signin.title': 'Connexion',
      'auth.signin.subtitle': 'Entrez vos informations pour accéder à votre compte',
      'auth.signup.title': 'Créer un nouveau compte',
      'auth.signup.subtitle': 'Créez un nouveau compte pour commencer',
      'auth.email': 'Email',
      'auth.password': 'Mot de passe',
      'auth.fullname': 'Nom complet',
      'auth.signin.button': 'Se connecter',
      'auth.signup.button': 'Créer un compte',
      'auth.google': 'Continuer avec Google',
      'auth.switch.signin': 'Vous avez un compte ? Connectez-vous',
      'auth.switch.signup': 'Pas de compte ? Inscrivez-vous',
      
      // Common
      'common.loading': 'Chargement...',
      'common.error': 'Erreur',
      'common.success': 'Succès',
      'common.cancel': 'Annuler',
      'common.save': 'Sauvegarder',
      'common.edit': 'Modifier',
      'common.delete': 'Supprimer',
    },
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'ar',
    debug: false,
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
