import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { AdminService, CreateUserData } from '@/services/adminService';
import { UserPlus, Loader2, X } from 'lucide-react';

interface UserCreationFormProps {
  onUserCreated: () => void;
  onClose: () => void;
}

const SPECIALIZATIONS = [
  'family', 'civil', 'commercial', 'criminal', 'labor', 'real_estate', 
  'tax', 'immigration', 'intellectual_property', 'environmental'
];

const SPECIALIZATION_LABELS: Record<string, string> = {
  family: 'قانون الأسرة',
  civil: 'القانون المدني',
  commercial: 'القانون التجاري',
  criminal: 'القانون الجنائي',
  labor: 'قانون العمل',
  real_estate: 'قانون العقارات',
  tax: 'القانون الضريبي',
  immigration: 'قانون الهجرة',
  intellectual_property: 'الملكية الفكرية',
  environmental: 'القانون البيئي'
};

export const UserCreationForm: React.FC<UserCreationFormProps> = ({ onUserCreated, onClose }) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<CreateUserData>({
    email: '',
    name: '',
    role: 'user',
    phone: '',
    subscription_tier: 'free',
    is_verified: true,
    advocate_specializations: [],
    advocate_bio: '',
    advocate_hourly_rate: 500
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { data, error } = await AdminService.createUser(formData);

      if (error) {
        throw error;
      }

      if (data?.success) {
        toast({
          title: 'نجح إنشاء المستخدم',
          description: `تم إنشاء حساب ${formData.email} بنجاح`,
        });
        onUserCreated();
        onClose();
      } else {
        throw new Error(data?.message || 'فشل في إنشاء المستخدم');
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في إنشاء المستخدم',
        description: error.message || 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSpecializationToggle = (specialization: string) => {
    const current = formData.advocate_specializations || [];
    const updated = current.includes(specialization)
      ? current.filter(s => s !== specialization)
      : [...current, specialization];
    
    setFormData({ ...formData, advocate_specializations: updated });
  };

  return (
    <Card className="w-full mx-auto bg-white shadow-lg">
      <CardHeader className="sticky top-0 bg-white border-b z-10">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              إنشاء مستخدم جديد
            </CardTitle>
            <CardDescription>
              إضافة مستخدم أو محامي جديد إلى النظام
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="max-h-[calc(100vh-200px)] overflow-y-auto">
        <form onSubmit={handleSubmit} className="space-y-6 py-4">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">الاسم الكامل *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="الاسم الكامل"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">رقم الهاتف</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="+212 6XX XXX XXX"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">نوع الحساب *</Label>
              <Select
                value={formData.role}
                onValueChange={(value: 'user' | 'advocate') => 
                  setFormData({ ...formData, role: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">مستخدم عادي</SelectItem>
                  <SelectItem value="advocate">محامي</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="subscription">نوع الاشتراك</Label>
              <Select
                value={formData.subscription_tier}
                onValueChange={(value: 'free' | 'pro_user' | 'pro_advocate') => 
                  setFormData({ ...formData, subscription_tier: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="free">مجاني</SelectItem>
                  <SelectItem value="pro_user">مستخدم متميز</SelectItem>
                  <SelectItem value="pro_advocate">محامي متميز</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2 pt-6">
              <Switch
                id="verified"
                checked={formData.is_verified}
                onCheckedChange={(checked) => 
                  setFormData({ ...formData, is_verified: checked })
                }
              />
              <Label htmlFor="verified">حساب مفعل</Label>
            </div>
          </div>

          {/* Advocate-specific fields */}
          {formData.role === 'advocate' && (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
              <h3 className="font-medium text-lg">معلومات المحامي</h3>
              
              <div className="space-y-2">
                <Label>التخصصات</Label>
                <div className="flex flex-wrap gap-2">
                  {SPECIALIZATIONS.map((spec) => (
                    <Badge
                      key={spec}
                      variant={formData.advocate_specializations?.includes(spec) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => handleSpecializationToggle(spec)}
                    >
                      {SPECIALIZATION_LABELS[spec]}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">نبذة تعريفية</Label>
                <Textarea
                  id="bio"
                  value={formData.advocate_bio}
                  onChange={(e) => setFormData({ ...formData, advocate_bio: e.target.value })}
                  placeholder="نبذة مختصرة عن المحامي وخبراته..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="hourly_rate">السعر بالساعة (درهم)</Label>
                <Input
                  id="hourly_rate"
                  type="number"
                  value={formData.advocate_hourly_rate}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    advocate_hourly_rate: parseFloat(e.target.value) || 500 
                  })}
                  min="0"
                  step="50"
                />
              </div>
            </div>
          )}

          {/* Submit buttons */}
          <div className="flex justify-end gap-3 pt-4 sticky bottom-0 bg-white border-t -mx-6 px-6 py-4 mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              إلغاء
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  جاري الإنشاء...
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  إنشاء المستخدم
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
