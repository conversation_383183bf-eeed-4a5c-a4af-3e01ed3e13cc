
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Scale, Users, FileText, MessageCircle } from 'lucide-react';

export const HeroSection = () => {
  const { t } = useTranslation();

  return (
    <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-12 sm:py-20">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-blue-600 p-4 rounded-full">
              <Scale className="h-12 w-12 text-white" />
            </div>
          </div>
          
          <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            {t('hero.title')}
          </h1>
          
          <p className="text-xl text-gray-600 mb-4">
            {t('hero.subtitle')}
          </p>
          
          <p className="text-lg text-gray-500 mb-8 max-w-2xl mx-auto">
            {t('hero.description')}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Button size="lg" className="text-lg px-8 py-4">
              {t('hero.cta.primary')}
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-4">
              {t('hero.cta.secondary')}
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 lg:gap-8 mt-12 sm:mt-16">
            <div className="text-center">
              <div className="bg-white p-4 rounded-full inline-block mb-4 shadow-md">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">محامون معتمدون</h3>
              <p className="text-gray-600">شبكة من المحامين المؤهلين والمعتمدين</p>
            </div>
            
            <div className="text-center">
              <div className="bg-white p-4 rounded-full inline-block mb-4 shadow-md">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">وثائق قانونية</h3>
              <p className="text-gray-600">إنشاء وتحرير الوثائق القانونية بسهولة</p>
            </div>
            
            <div className="text-center">
              <div className="bg-white p-4 rounded-full inline-block mb-4 shadow-md">
                <MessageCircle className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">استشارات فورية</h3>
              <p className="text-gray-600">احصل على إجابات سريعة لأسئلتك القانونية</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
