import { useEffect, useState } from 'react';
import { fetchDocuments, downloadDocument, Document } from '@/services/apiService';
import { DocumentCard } from './DocumentCard';
import { DocumentSkeleton } from './DocumentSkeleton';

export const DocumentList = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      const docs = await fetchDocuments();
      setDocuments(docs);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (id: string, filename: string) => {
    try {
      await downloadDocument(id, filename);
    } catch (error) {
      console.error('Error downloading document:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" dir="rtl">
        {Array.from({ length: 6 }).map((_, index) => (
          <DocumentSkeleton key={index} />
        ))}
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="text-center py-12" dir="rtl">
        <div className="text-gray-500 mb-4">لا توجد وثائق متاحة</div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" dir="rtl">
      {documents.map((doc) => (
        <DocumentCard
          key={doc.id}
          name={doc.name}
          onDownload={() => handleDownload(doc.id, doc.name)}
        />
      ))}
    </div>
  );
};
