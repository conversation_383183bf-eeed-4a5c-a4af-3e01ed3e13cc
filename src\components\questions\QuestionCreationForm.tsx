import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { QuestionsService } from '@/services/questionsService';
import { 
  MessageCircle, 
  Upload, 
  X, 
  Star, 
  Clock, 
  DollarSign, 
  User,
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface Advocate {
  id: string;
  profile_id: string;
  specializations: string[];
  bio: string;
  hourly_rate: number;
  rating: number;
  total_reviews: number;
  is_featured: boolean;
  profiles: {
    full_name: string;
    email: string;
    is_verified: boolean;
  };
}

interface QuestionFormData {
  title: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  advocate_id: string;
  attachments: File[];
}

interface QuestionCreationFormProps {
  onQuestionCreated: (question: any) => void;
  onClose: () => void;
  userId: string;
}

const CATEGORIES = [
  { value: 'family', label: 'قانون الأسرة', icon: '👨‍👩‍👧‍👦' },
  { value: 'commercial', label: 'القانون التجاري', icon: '🏢' },
  { value: 'criminal', label: 'القانون الجنائي', icon: '⚖️' },
  { value: 'civil', label: 'القانون المدني', icon: '📋' },
  { value: 'labor', label: 'قانون العمل', icon: '👷' },
  { value: 'real_estate', label: 'قانون العقارات', icon: '🏠' },
  { value: 'tax', label: 'القانون الضريبي', icon: '💰' },
  { value: 'administrative', label: 'القانون الإداري', icon: '🏛️' },
  { value: 'intellectual_property', label: 'الملكية الفكرية', icon: '💡' },
  { value: 'general', label: 'عام', icon: '📝' }
];

const PRIORITY_LEVELS = [
  { value: 'low', label: 'منخفضة', color: 'bg-green-100 text-green-800' },
  { value: 'medium', label: 'متوسطة', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: 'عالية', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'عاجلة', color: 'bg-red-100 text-red-800' }
];

export const QuestionCreationForm: React.FC<QuestionCreationFormProps> = ({
  onQuestionCreated,
  onClose,
  userId
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [advocates, setAdvocates] = useState<Advocate[]>([]);
  const [loadingAdvocates, setLoadingAdvocates] = useState(true);
  const [formData, setFormData] = useState<QuestionFormData>({
    title: '',
    description: '',
    category: '',
    priority: 'medium',
    advocate_id: '',
    attachments: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchAdvocates();
  }, []);

  const fetchAdvocates = async () => {
    setLoadingAdvocates(true);
    try {
      const { data, error } = await supabase
        .from('advocates')
        .select(`
          id,
          profile_id,
          specializations,
          bio,
          hourly_rate,
          rating,
          total_reviews,
          is_featured,
          profiles!advocates_profile_id_fkey(
            full_name,
            email,
            is_verified
          )
        `)
        .eq('profiles.is_verified', true)
        .order('rating', { ascending: false });

      if (error) throw error;
      setAdvocates(data || []);
    } catch (error: any) {
      console.error('Error fetching advocates:', error);
      toast({
        title: 'خطأ في تحميل المحامين',
        description: 'فشل في تحميل قائمة المحامين المتاحين',
        variant: 'destructive',
      });
    } finally {
      setLoadingAdvocates(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان السؤال مطلوب';
    } else if (formData.title.length < 10) {
      newErrors.title = 'عنوان السؤال يجب أن يكون 10 أحرف على الأقل';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف السؤال مطلوب';
    } else if (formData.description.length < 20) {
      newErrors.description = 'وصف السؤال يجب أن يكون 20 حرف على الأقل';
    }

    if (!formData.category) {
      newErrors.category = 'يرجى اختيار تصنيف السؤال';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        toast({
          title: 'حجم الملف كبير جداً',
          description: `الملف ${file.name} أكبر من 5 ميجابايت`,
          variant: 'destructive',
        });
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: 'نوع ملف غير مدعوم',
          description: `الملف ${file.name} من نوع غير مدعوم`,
          variant: 'destructive',
        });
        return false;
      }
      return true;
    });

    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...validFiles].slice(0, 3) // Max 3 files
    }));
  };

  const removeAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      // Create the question with priority
      const { data: questionData, error: questionError } = await QuestionsService.createQuestion(
        userId,
        formData.title,
        formData.description,
        formData.category,
        formData.priority,
        formData.advocate_id || undefined
      );

      if (questionError) throw questionError;

      // If an advocate was selected, assign the question
      if (formData.advocate_id && questionData) {
        const { error: assignError } = await QuestionsService.assignQuestionToAdvocate(
          questionData.id, 
          formData.advocate_id
        );
        if (assignError) {
          console.warn('Failed to assign advocate:', assignError);
        }
      }

      // Handle file uploads (simplified - in production you'd upload to storage)
      if (formData.attachments.length > 0) {
        // For now, we'll just log the files
        console.log('Files to upload:', formData.attachments.map(f => f.name));
        // TODO: Implement file upload to Supabase Storage
      }

      toast({
        title: 'تم إرسال السؤال بنجاح',
        description: formData.advocate_id 
          ? 'تم تعيين المحامي وسيتم الرد قريباً' 
          : 'سيتم تعيين محامي مناسب قريباً',
      });

      onQuestionCreated(questionData);
      onClose();
    } catch (error: any) {
      console.error('Error creating question:', error);
      toast({
        title: 'خطأ في إرسال السؤال',
        description: error.message || 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getAdvocatesByCategory = () => {
    if (!formData.category) return advocates;
    return advocates.filter(advocate => 
      advocate.specializations.includes(formData.category)
    );
  };

  const selectedCategory = CATEGORIES.find(cat => cat.value === formData.category);
  const filteredAdvocates = getAdvocatesByCategory();

  return (
    <Card className="w-full max-w-4xl mx-auto max-h-[90vh] overflow-y-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              طرح سؤال قانوني جديد
            </CardTitle>
            <CardDescription>
              اكتب سؤالك بوضوح واختر المحامي المناسب للحصول على أفضل إجابة
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Question Title */}
          <div className="space-y-2">
            <Label htmlFor="title">عنوان السؤال *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="اكتب عنوان واضح ومختصر لسؤالك"
              className={errors.title ? 'border-red-500' : ''}
            />
            {errors.title && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Question Description */}
          <div className="space-y-2">
            <Label htmlFor="description">تفاصيل السؤال *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="اشرح سؤالك بالتفصيل مع ذكر جميع المعلومات المهمة..."
              rows={4}
              className={errors.description ? 'border-red-500' : ''}
            />
            {errors.description && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.description}
              </p>
            )}
            <p className="text-sm text-gray-500">
              {formData.description.length}/500 حرف
            </p>
          </div>

          {/* Category and Priority */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">تصنيف السؤال *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData({ ...formData, category: value, advocate_id: '' })}
              >
                <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                  <SelectValue placeholder="اختر تصنيف السؤال" />
                </SelectTrigger>
                <SelectContent>
                  {CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      <div className="flex items-center gap-2">
                        <span>{category.icon}</span>
                        <span>{category.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.category}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">مستوى الأولوية</Label>
              <Select
                value={formData.priority}
                onValueChange={(value: any) => setFormData({ ...formData, priority: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PRIORITY_LEVELS.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      <Badge className={priority.color}>
                        {priority.label}
                      </Badge>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* File Attachments */}
          <div className="space-y-2">
            <Label htmlFor="attachments">المرفقات (اختياري)</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                id="attachments"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={handleFileUpload}
                className="hidden"
              />
              <label
                htmlFor="attachments"
                className="flex flex-col items-center justify-center cursor-pointer"
              >
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">
                  اضغط لرفع الملفات أو اسحبها هنا
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  PDF, DOC, DOCX, JPG, PNG (حد أقصى 5 ميجابايت لكل ملف)
                </p>
              </label>
            </div>
            
            {formData.attachments.length > 0 && (
              <div className="space-y-2">
                {formData.attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">{file.name}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAttachment(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Advocate Selection */}
          {selectedCategory && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Label>اختيار المحامي</Label>
                <Badge variant="outline">
                  {selectedCategory.icon} {selectedCategory.label}
                </Badge>
              </div>
              
              {loadingAdvocates ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="mr-2">جاري تحميل المحامين...</span>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                       onClick={() => setFormData({ ...formData, advocate_id: '' })}>
                    <div className="flex items-center gap-3">
                      <input
                        type="radio"
                        checked={formData.advocate_id === ''}
                        onChange={() => setFormData({ ...formData, advocate_id: '' })}
                      />
                      <div>
                        <p className="font-medium">تعيين تلقائي</p>
                        <p className="text-sm text-gray-600">
                          سيتم اختيار أفضل محامي متخصص في {selectedCategory.label}
                        </p>
                      </div>
                    </div>
                  </div>

                  {filteredAdvocates.map((advocate) => (
                    <div
                      key={advocate.profile_id}
                      className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                        formData.advocate_id === advocate.profile_id ? 'border-blue-500 bg-blue-50' : ''
                      }`}
                      onClick={() => setFormData({ ...formData, advocate_id: advocate.profile_id })}
                    >
                      <div className="flex items-start gap-3">
                        <input
                          type="radio"
                          checked={formData.advocate_id === advocate.profile_id}
                          onChange={() => setFormData({ ...formData, advocate_id: advocate.profile_id })}
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">{advocate.profiles.full_name}</h4>
                            {advocate.is_featured && (
                              <Badge variant="secondary">مميز</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 text-yellow-500" />
                              <span>{advocate.rating.toFixed(1)}</span>
                              <span>({advocate.total_reviews} تقييم)</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              <span>{advocate.hourly_rate} درهم/ساعة</span>
                            </div>
                          </div>
                          <p className="text-sm text-gray-700 line-clamp-2">
                            {advocate.bio || 'محامي متخصص'}
                          </p>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {advocate.specializations.slice(0, 3).map((spec) => (
                              <Badge key={spec} variant="outline" className="text-xs">
                                {CATEGORIES.find(c => c.value === spec)?.label || spec}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {filteredAdvocates.length === 0 && (
                    <div className="text-center py-4 text-gray-500">
                      <User className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      <p>لا يوجد محامين متخصصين في هذا المجال حالياً</p>
                      <p className="text-sm">سيتم التعيين التلقائي</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Submit Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              إلغاء
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  جاري الإرسال...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  إرسال السؤال
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
