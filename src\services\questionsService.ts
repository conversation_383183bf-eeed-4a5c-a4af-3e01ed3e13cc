import { supabase } from '@/integrations/supabase/client';

export interface LegalQuestion {
  id: string;
  user_id: string;
  advocate_id?: string;
  title: string;
  description: string;
  category?: string;
  status: string;
  is_answered: boolean;
  created_at: string;
  updated_at: string;
  user_profile?: {
    full_name: string;
    email: string;
  };
  advocate_profile?: {
    full_name: string;
    email: string;
  };
  answer?: string;
  answer_created_at?: string;
}

export interface QuestionResponse {
  id: string;
  question_id: string;
  advocate_id: string;
  response_text: string;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
  advocate_profile?: {
    full_name: string;
    email: string;
  };
}

export class QuestionsService {
  static async createQuestion(
    userId: string,
    title: string,
    description: string,
    category?: string,
    priority?: string,
    preferredAdvocateId?: string
  ) {
    try {
      console.log('🔄 Creating question for user:', userId);

      // Direct insertion with enhanced validation
      console.log('📝 Using direct insertion method');

      // Validate inputs
      if (!userId || !title || !description) {
        throw new Error('معلومات السؤال غير مكتملة');
      }

      if (title.length < 10) {
        throw new Error('عنوان السؤال يجب أن يكون 10 أحرف على الأقل');
      }

      if (description.length < 20) {
        throw new Error('وصف السؤال يجب أن يكون 20 حرف على الأقل');
      }

      const questionData = {
        user_id: userId,
        title: title.trim(),
        description: description.trim(),
        category: category || 'general',
        status: 'pending',
        is_answered: false,
        priority: priority || 'medium',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📝 Question data:', questionData);

      const { data, error } = await supabase
        .from('legal_questions')
        .insert(questionData)
        .select()
        .single();

      if (error) {
        console.error('❌ Database error:', error);
        throw new Error(`فشل في حفظ السؤال: ${error.message}`);
      }

      console.log('✅ Question created successfully:', data);

      // If a preferred advocate was specified, try to assign the question
      if (preferredAdvocateId && data) {
        try {
          console.log('🔄 Assigning question to preferred advocate:', preferredAdvocateId);
          await this.assignQuestionToAdvocate(data.id, preferredAdvocateId);
          console.log('✅ Question assigned to advocate successfully');
        } catch (assignError) {
          console.warn('⚠️ Failed to assign to preferred advocate:', assignError);
          // Don't fail the whole operation if assignment fails
        }
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('❌ Error creating question:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('حدث خطأ غير متوقع')
      };
    }
  }

  static async getUserQuestions(userId: string) {
    try {
      console.log('🔍 QuestionsService.getUserQuestions called for user:', userId);

      // Essayer d'abord une requête simple
      const { data: simpleData, error: simpleError } = await supabase
        .from('legal_questions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (simpleError) {
        console.error('❌ Simple query failed:', simpleError);
        throw simpleError;
      }

      console.log('✅ Simple query successful:', simpleData?.length || 0, 'questions');
      return { data: simpleData, error: null };

    } catch (error) {
      console.error('❌ Critical error in getUserQuestions:', error);
      return { data: null, error };
    }
  }

  static async getAdvocateQuestions(advocateId: string) {
    try {
      // First get the advocate profile to get the profile_id
      const { data: advocateProfile, error: advocateError } = await supabase
        .from('advocates')
        .select('id')
        .eq('profile_id', advocateId)
        .single();

      if (advocateError) throw advocateError;

      const { data, error } = await supabase
        .from('legal_questions')
        .select(`
          *,
          profiles!legal_questions_user_id_fkey(
            full_name,
            email
          )
        `)
        .eq('advocate_id', advocateProfile.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching advocate questions:', error);
      return { data: null, error };
    }
  }

  static async getPendingQuestions() {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .select(`
          *,
          profiles!legal_questions_user_id_fkey(
            full_name,
            email
          )
        `)
        .is('advocate_id', null)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching pending questions:', error);
      return { data: null, error };
    }
  }



  static async createResponse(questionId: string, responseText: string, advocateProfileId: string) {
    try {
      // First get the advocate ID from the profile ID
      const { data: advocateProfile, error: advocateError } = await supabase
        .from('advocates')
        .select('id')
        .eq('profile_id', advocateProfileId)
        .single();

      if (advocateError) throw advocateError;

      // Create the response
      const { data, error } = await supabase
        .from('responses')
        .insert({
          question_id: questionId,
          advocate_id: advocateProfile.id,
          response_text: responseText,
          is_approved: true, // Auto-approve for now
        })
        .select()
        .single();

      if (error) throw error;

      // The trigger will automatically update the question status
      return { data, error: null };
    } catch (error) {
      console.error('Error creating response:', error);
      return { data: null, error };
    }
  }

  static async getQuestionResponses(questionId: string) {
    try {
      const { data, error } = await supabase
        .from('responses')
        .select(`
          *,
          advocates!responses_advocate_id_fkey(
            profiles!advocates_profile_id_fkey(
              full_name,
              email
            )
          )
        `)
        .eq('question_id', questionId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching question responses:', error);
      return { data: null, error };
    }
  }

  static async assignQuestionToAdvocate(questionId: string, advocateProfileId: string) {
    try {
      // First get the advocate ID from the profile ID
      const { data: advocateProfile, error: advocateError } = await supabase
        .from('advocates')
        .select('id')
        .eq('profile_id', advocateProfileId)
        .single();

      if (advocateError) throw advocateError;

      const { data, error } = await supabase
        .from('legal_questions')
        .update({
          advocate_id: advocateProfile.id,
          status: 'assigned',
          updated_at: new Date().toISOString(),
        })
        .eq('id', questionId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error assigning question to advocate:', error);
      return { data: null, error };
    }
  }

  static async getAllQuestions() {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .select(`
          *,
          profiles!legal_questions_user_id_fkey(
            full_name,
            email
          ),
          advocates!legal_questions_advocate_id_fkey(
            id,
            profiles!advocates_profile_id_fkey(
              full_name,
              email
            )
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching all questions:', error);
      return { data: null, error };
    }
  }

  static async getQuestionById(questionId: string) {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .select(`
          *,
          profiles!legal_questions_user_id_fkey(
            full_name,
            email
          ),
          advocates!legal_questions_advocate_id_fkey(
            id,
            profiles!advocates_profile_id_fkey(
              full_name,
              email
            )
          )
        `)
        .eq('id', questionId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching question by ID:', error);
      return { data: null, error };
    }
  }

  static async deleteQuestion(questionId: string, userId: string) {
    try {
      const { data, error } = await supabase
        .from('legal_questions')
        .delete()
        .eq('id', questionId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error deleting question:', error);
      return { data: null, error };
    }
  }

  static getQuestionCategories() {
    return [
      { value: 'family', label: 'قانون الأسرة', labelFr: 'Droit de la famille' },
      { value: 'labor', label: 'قانون العمل', labelFr: 'Droit du travail' },
      { value: 'real_estate', label: 'قانون العقارات', labelFr: 'Droit immobilier' },
      { value: 'commercial', label: 'القانون التجاري', labelFr: 'Droit commercial' },
      { value: 'criminal', label: 'القانون الجنائي', labelFr: 'Droit pénal' },
      { value: 'civil', label: 'القانون المدني', labelFr: 'Droit civil' },
      { value: 'administrative', label: 'القانون الإداري', labelFr: 'Droit administratif' },
      { value: 'tax', label: 'القانون الضريبي', labelFr: 'Droit fiscal' },
      { value: 'intellectual_property', label: 'الملكية الفكرية', labelFr: 'Propriété intellectuelle' },
      { value: 'general', label: 'عام', labelFr: 'Général' },
    ];
  }
}
