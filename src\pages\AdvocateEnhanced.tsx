import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/hooks/use-toast';
import { Navigate } from 'react-router-dom';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  LayoutDashboard, 
  MessageSquare, 
  User, 
  BarChart3, 
  FileText, 
  Search,
  Filter,
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  DollarSign,
  TrendingUp,
  Users,
  Calendar,
  Settings,
  Bell,
  Upload,
  Download,
  Edit,
  Eye,
  MessageCircle,
  Phone,
  Mail
} from 'lucide-react';
import { QuestionsService, LegalQuestion } from '@/services/questionsService';
import { AuthService, AdvocateProfile } from '@/services/authService';
import Loading from '@/components/ui/loading';
import { AdvocateProfileEdit } from '@/components/advocate/AdvocateProfileEdit';

interface AdvocateStats {
  totalQuestions: number;
  pendingQuestions: number;
  completedQuestions: number;
  averageResponseTime: number;
  totalEarnings: number;
  monthlyEarnings: number;
  clientSatisfaction: number;
  activeClients: number;
}

const AdvocateEnhanced = () => {
  const { t } = useTranslation();
  const { user, profile } = useAuth();
  const { toast } = useToast();
  
  // State management
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [questions, setQuestions] = useState<LegalQuestion[]>([]);
  const [pendingQuestions, setPendingQuestions] = useState<LegalQuestion[]>([]);
  const [advocateProfile, setAdvocateProfile] = useState<AdvocateProfile | null>(null);
  const [stats, setStats] = useState<AdvocateStats>({
    totalQuestions: 0,
    pendingQuestions: 0,
    completedQuestions: 0,
    averageResponseTime: 0,
    totalEarnings: 0,
    monthlyEarnings: 0,
    clientSatisfaction: 0,
    activeClients: 0
  });
  
  // Filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');

  useEffect(() => {
    if (user && profile?.role === 'advocate') {
      loadAdvocateData();
    }
  }, [user, profile]);

  const loadAdvocateData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Load advocate profile
      const advocateData = await AuthService.getAdvocateProfile(user.id);
      setAdvocateProfile(advocateData);

      // Load assigned questions
      const { data: assignedQuestions } = await QuestionsService.getAdvocateQuestions(user.id);
      setQuestions(assignedQuestions || []);

      // Load pending questions (available to take)
      const { data: pendingQuestionsData } = await QuestionsService.getPendingQuestions();
      setPendingQuestions(pendingQuestionsData || []);

      // Calculate stats
      calculateStats(assignedQuestions || [], advocateData);

    } catch (error) {
      console.error('Error loading advocate data:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تحميل البيانات',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (questions: LegalQuestion[], profile: AdvocateProfile | null) => {
    const completed = questions.filter(q => q.is_answered).length;
    const pending = questions.filter(q => !q.is_answered).length;
    const hourlyRate = profile?.hourly_rate || 500;
    
    setStats({
      totalQuestions: questions.length,
      pendingQuestions: pending,
      completedQuestions: completed,
      averageResponseTime: 2.5, // Mock data
      totalEarnings: completed * hourlyRate,
      monthlyEarnings: completed * hourlyRate * 0.3, // Mock calculation
      clientSatisfaction: profile?.rating || 0,
      activeClients: questions.length > 0 ? Math.ceil(questions.length / 2) : 0
    });
  };

  const handleTakeQuestion = async (questionId: string) => {
    if (!user) return;

    try {
      const { error } = await QuestionsService.assignQuestionToAdvocate(questionId, user.id);
      
      if (error) throw error;

      toast({
        title: 'تم بنجاح',
        description: 'تم تعيين السؤال لك بنجاح',
      });

      // Reload data
      loadAdvocateData();
    } catch (error) {
      console.error('Error taking question:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تعيين السؤال',
        variant: 'destructive',
      });
    }
  };

  // Filter questions based on search and filters
  const filteredQuestions = questions.filter(question => {
    const matchesSearch = question.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         question.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || question.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || question.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const filteredPendingQuestions = pendingQuestions.filter(question => {
    const matchesSearch = question.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         question.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || question.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  if (loading && !user) {
    return <Loading fullScreen text="جاري التحميل..." />;
  }

  if (!user || profile?.role !== 'advocate') {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                لوحة تحكم المحامي
              </h1>
              <p className="text-gray-600 mt-1">
                مرحباً {advocateProfile?.bio ? 'محامي' : ''} {profile?.full_name || user.email}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                الإشعارات
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                الإعدادات
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <LayoutDashboard className="h-4 w-4" />
              الرئيسية
            </TabsTrigger>
            <TabsTrigger value="questions" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              الأسئلة
            </TabsTrigger>
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              الملف الشخصي
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              الإحصائيات
            </TabsTrigger>
            <TabsTrigger value="documents" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              المستندات
            </TabsTrigger>
          </TabsList>

          {/* Dashboard Tab */}
          <TabsContent value="dashboard" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">إجمالي الأسئلة</CardTitle>
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalQuestions}</div>
                  <p className="text-xs text-muted-foreground">
                    +{stats.pendingQuestions} في الانتظار
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">الأسئلة المكتملة</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.completedQuestions}</div>
                  <p className="text-xs text-muted-foreground">
                    معدل الإنجاز {stats.totalQuestions > 0 ? Math.round((stats.completedQuestions / stats.totalQuestions) * 100) : 0}%
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">الأرباح الشهرية</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.monthlyEarnings.toLocaleString()} درهم</div>
                  <p className="text-xs text-muted-foreground">
                    +12% من الشهر الماضي
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">تقييم العملاء</CardTitle>
                  <Star className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.clientSatisfaction.toFixed(1)}</div>
                  <p className="text-xs text-muted-foreground">
                    من 5 نجوم
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>الإجراءات السريعة</CardTitle>
                <CardDescription>
                  الإجراءات الأكثر استخداماً
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col"
                    onClick={() => setActiveTab('questions')}
                  >
                    <MessageSquare className="h-6 w-6 mb-2" />
                    عرض الأسئلة
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col"
                    onClick={() => setActiveTab('profile')}
                  >
                    <Edit className="h-6 w-6 mb-2" />
                    تحديث الملف
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col"
                    onClick={() => setActiveTab('analytics')}
                  >
                    <BarChart3 className="h-6 w-6 mb-2" />
                    الإحصائيات
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col"
                    onClick={() => setActiveTab('documents')}
                  >
                    <Upload className="h-6 w-6 mb-2" />
                    رفع مستند
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recent Questions */}
            <Card>
              <CardHeader>
                <CardTitle>الأسئلة الحديثة</CardTitle>
                <CardDescription>
                  آخر الأسئلة المطروحة
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {questions.slice(0, 3).map((question) => (
                    <div key={question.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{question.title}</h4>
                        <p className="text-sm text-muted-foreground line-clamp-1">
                          {question.description}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline">{question.category}</Badge>
                          <Badge variant={question.is_answered ? "default" : "secondary"}>
                            {question.is_answered ? "مجاب عليه" : "في الانتظار"}
                          </Badge>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        عرض
                      </Button>
                    </div>
                  ))}
                  {questions.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      لا توجد أسئلة حالياً
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Questions Tab */}
          <TabsContent value="questions" className="space-y-6">
            {/* Search and Filters */}
            <Card>
              <CardHeader>
                <CardTitle>البحث والتصفية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="البحث في الأسئلة..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="حالة السؤال" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="pending">في الانتظار</SelectItem>
                      <SelectItem value="assigned">مُعيَّن</SelectItem>
                      <SelectItem value="completed">مكتمل</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="التخصص" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع التخصصات</SelectItem>
                      <SelectItem value="family">قانون الأسرة</SelectItem>
                      <SelectItem value="commercial">القانون التجاري</SelectItem>
                      <SelectItem value="civil">القانون المدني</SelectItem>
                      <SelectItem value="criminal">القانون الجنائي</SelectItem>
                      <SelectItem value="labor">قانون العمل</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger>
                      <SelectValue placeholder="ترتيب حسب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="created_at">تاريخ الإنشاء</SelectItem>
                      <SelectItem value="priority">الأولوية</SelectItem>
                      <SelectItem value="category">التخصص</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* My Questions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  أسئلتي المُعيَّنة ({filteredQuestions.length})
                </CardTitle>
                <CardDescription>
                  الأسئلة المُعيَّنة لك للإجابة عليها
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredQuestions.map((question) => (
                    <div key={question.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{question.title}</h3>
                            <Badge variant="outline">{question.category}</Badge>
                            <Badge variant={question.is_answered ? "default" : "secondary"}>
                              {question.is_answered ? "مجاب عليه" : "في الانتظار"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                            {question.description}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(question.created_at).toLocaleDateString('ar')}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              منذ {Math.floor((Date.now() - new Date(question.created_at).getTime()) / (1000 * 60 * 60 * 24))} أيام
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2">
                          <Button size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            عرض التفاصيل
                          </Button>
                          {!question.is_answered && (
                            <Button size="sm" variant="outline">
                              <MessageCircle className="h-4 w-4 mr-2" />
                              الإجابة
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  {filteredQuestions.length === 0 && (
                    <div className="text-center py-12">
                      <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-muted-foreground mb-2">
                        لا توجد أسئلة مُعيَّنة
                      </h3>
                      <p className="text-muted-foreground">
                        لم يتم تعيين أي أسئلة لك بعد. تحقق من الأسئلة المتاحة أدناه.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Available Questions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  الأسئلة المتاحة ({filteredPendingQuestions.length})
                </CardTitle>
                <CardDescription>
                  أسئلة يمكنك أخذها والإجابة عليها
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredPendingQuestions.map((question) => (
                    <div key={question.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{question.title}</h3>
                            <Badge variant="outline">{question.category}</Badge>
                            <Badge variant="secondary">متاح</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                            {question.description}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(question.created_at).toLocaleDateString('ar')}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              منذ {Math.floor((Date.now() - new Date(question.created_at).getTime()) / (1000 * 60 * 60 * 24))} أيام
                            </span>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleTakeQuestion(question.id)}
                          >
                            أخذ السؤال
                          </Button>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-2" />
                            معاينة
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  {filteredPendingQuestions.length === 0 && (
                    <div className="text-center py-12">
                      <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-muted-foreground mb-2">
                        لا توجد أسئلة متاحة
                      </h3>
                      <p className="text-muted-foreground">
                        جميع الأسئلة مُعيَّنة حالياً أو لا توجد أسئلة جديدة.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdvocateEnhanced;
